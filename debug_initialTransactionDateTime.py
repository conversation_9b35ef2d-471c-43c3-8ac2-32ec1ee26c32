#!/usr/bin/env python3
"""
Debug script to investigate initialTransactionDateTime field access issues
"""
import json
import sys
from pprint import pprint

def analyze_initialTransactionDateTime(json_file_path, position_index=0):
    """Analyze the initialTransactionDateTime field structure"""
    
    print("=" * 80)
    print("INITIAL TRANSACTION DATETIME FIELD ANALYSIS")
    print("=" * 80)
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✓ Successfully loaded JSON file: {json_file_path}")
        print(f"✓ Total positions: {len(data)}")
        print()
        
        if position_index >= len(data):
            print(f"❌ Position index {position_index} is out of range (max: {len(data)-1})")
            return
        
        position = data[position_index]
        
        print(f"ANALYZING POSITION {position_index}:")
        print("-" * 50)
        
        # Check if initialTransactionDateTime exists at position level
        print("1. POSITION LEVEL ANALYSIS:")
        if 'initialTransactionDateTime' in position:
            print("   ✓ position['initialTransactionDateTime'] EXISTS")
            initial_dt = position['initialTransactionDateTime']
            print(f"   Type: {type(initial_dt)}")
            print(f"   Value: {initial_dt}")
            
            if isinstance(initial_dt, dict):
                print("   Keys in initialTransactionDateTime:")
                for key in initial_dt.keys():
                    print(f"     - {key}: {initial_dt[key]} (type: {type(initial_dt[key])})")
                
                # Test the specific access pattern used in code
                if 'dateTime' in initial_dt:
                    print(f"   ✓ position['initialTransactionDateTime']['dateTime']: {initial_dt['dateTime']}")
                else:
                    print("   ❌ 'dateTime' key NOT FOUND in initialTransactionDateTime")
                    print("   Available keys:", list(initial_dt.keys()))
            else:
                print(f"   ❌ initialTransactionDateTime is not a dict: {type(initial_dt)}")
        else:
            print("   ❌ position['initialTransactionDateTime'] DOES NOT EXIST")
            print("   Available position keys containing 'transaction' or 'date':")
            for key in position.keys():
                if 'transaction' in key.lower() or 'date' in key.lower():
                    print(f"     - {key}: {type(position[key])}")
        
        print()
        
        # Check trades level
        print("2. TRADES LEVEL ANALYSIS:")
        if 'Trades' in position:
            trades = position['Trades']
            print(f"   Number of trades: {len(trades)}")
            
            for i, trade in enumerate(trades[:2]):  # Check first 2 trades
                print(f"   TRADE {i}:")
                
                # Check if initialTransactionDateTime exists at trade level
                if 'initialTransactionDateTime' in trade:
                    print("     ✓ trade['initialTransactionDateTime'] EXISTS")
                    trade_initial_dt = trade['initialTransactionDateTime']
                    print(f"     Type: {type(trade_initial_dt)}")
                    print(f"     Value: {trade_initial_dt}")
                    
                    if isinstance(trade_initial_dt, dict) and 'dateTime' in trade_initial_dt:
                        print(f"     ✓ trade['initialTransactionDateTime']['dateTime']: {trade_initial_dt['dateTime']}")
                else:
                    print("     ❌ trade['initialTransactionDateTime'] DOES NOT EXIST")
                
                # Check for similar fields
                print("     Available trade keys containing 'transaction' or 'date':")
                for key in trade.keys():
                    if 'transaction' in key.lower() or 'date' in key.lower():
                        print(f"       - {key}: {type(trade[key])}")
        else:
            print("   ❌ 'Trades' key not found in position")
        
        print()
        
        # Check for alternative field names
        print("3. ALTERNATIVE FIELD ANALYSIS:")
        print("   Looking for similar fields...")
        
        all_keys = set()
        all_keys.update(position.keys())
        if 'Trades' in position:
            for trade in position['Trades']:
                all_keys.update(trade.keys())
        
        similar_fields = [key for key in all_keys if 'initial' in key.lower() or 'transaction' in key.lower()]
        
        if similar_fields:
            print("   Found similar fields:")
            for field in similar_fields:
                if field in position:
                    print(f"     - position['{field}']: {type(position[field])}")
                if 'Trades' in position:
                    for i, trade in enumerate(position['Trades'][:1]):
                        if field in trade:
                            print(f"     - trade['{field}']: {type(trade[field])}")
        else:
            print("   No similar fields found")
        
        print()
        
        # Suggest fixes
        print("4. SUGGESTED FIXES:")
        print("-" * 30)
        
        # Test safe access
        def safe_get_nested(data, *keys, default=""):
            """Safely get nested dictionary value"""
            try:
                result = data
                for key in keys:
                    result = result[key]
                return result
            except (KeyError, TypeError):
                return default
        
        # Test different access patterns
        test_patterns = [
            ("position['initialTransactionDateTime']['dateTime']", 
             lambda: safe_get_nested(position, 'initialTransactionDateTime', 'dateTime')),
            ("position['initialTransactionDateTime']['date']", 
             lambda: safe_get_nested(position, 'initialTransactionDateTime', 'date')),
            ("position['initialTransactionDateTime']", 
             lambda: safe_get_nested(position, 'initialTransactionDateTime')),
        ]
        
        if 'Trades' in position and position['Trades']:
            trade = position['Trades'][0]
            test_patterns.extend([
                ("trade['initialTransactionDateTime']['dateTime']", 
                 lambda: safe_get_nested(trade, 'initialTransactionDateTime', 'dateTime')),
                ("trade['transactionDateTime']['dateTime']", 
                 lambda: safe_get_nested(trade, 'transactionDateTime', 'dateTime')),
            ])
        
        for pattern_desc, pattern_func in test_patterns:
            try:
                result = pattern_func()
                if result:
                    print(f"   ✓ {pattern_desc} = {result}")
                else:
                    print(f"   ❌ {pattern_desc} = (empty/not found)")
            except Exception as e:
                print(f"   ❌ {pattern_desc} = ERROR: {e}")
        
    except FileNotFoundError:
        print(f"❌ Error: File not found: {json_file_path}")
    except json.JSONDecodeError as e:
        print(f"❌ Error: Invalid JSON: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

def suggest_safe_access_code():
    """Suggest safe access code for initialTransactionDateTime"""
    
    print("\n" + "=" * 80)
    print("SUGGESTED SAFE ACCESS CODE")
    print("=" * 80)
    
    code_suggestion = '''
# Safe access function
def safe_get_datetime(data, *keys, default=""):
    """Safely get nested datetime value"""
    try:
        result = data
        for key in keys:
            result = result[key]
        return result if result else default
    except (KeyError, TypeError, AttributeError):
        return default

# Replace this problematic line:
# csv_record.append(position['initialTransactionDateTime']['dateTime'])

# With this safe version:
initial_dt = safe_get_datetime(position, 'initialTransactionDateTime', 'dateTime')
csv_record.append(initial_dt)

# Or even safer with fallback:
initial_dt = (safe_get_datetime(position, 'initialTransactionDateTime', 'dateTime') or 
              safe_get_datetime(position, 'initialTransactionDateTime', 'date') or
              safe_get_datetime(trade, 'transactionDateTime', 'dateTime') or
              "")
csv_record.append(initial_dt)
'''
    
    print(code_suggestion)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python debug_initialTransactionDateTime.py <json_file_path> [position_index]")
        print("Example: python debug_initialTransactionDateTime.py input/20250128_HKTR.json 0")
        sys.exit(1)
    
    json_file = sys.argv[1]
    position_idx = int(sys.argv[2]) if len(sys.argv) > 2 else 0
    
    analyze_initialTransactionDateTime(json_file, position_idx)
    suggest_safe_access_code()
