## Setup Development Environment
1. Install [PyCharm Community](https://www.jetbrains.com/pycharm/download/?section=windows)
   - Press **Cancel** when asked for admin user to continue to install to current user
   - ![install_pycharm_1.png](README/install_pycharm_1.png)
2. Launch PyCharm
3. Open Project and choose **\python\script** from your cloned Git repository
   - ![open_project_1.png](README/open_project_1.png)
4. Setup Python Interpreter for the Virtualenv Environment accordingly
5. Install Python Oracle Client
   - Open **Terminal** in PyCharm for your venv
   - Run `pip install --trusted-host pypi.org --trusted-host files.pythonhosted.org cx_Oracle --upgrade`

## Run Script
1. Make sure that the ENV is set
   - Open **/env/environment.properties**
   - Change to **ENV=dev**
2. Set **Python** Configuration
   - Set a Config for each script you would like to run
   - ![script_config.png](README/script_config.png)
3. Click **Run** or **Debug**