import sys

parameter = sys.argv
print('Parameters = ', sys.argv)
sourceFile = open('input/Common.config', 'r')
content = sourceFile.read()
print(content)
drContent = content.replace('SophisCSN1', 'SophisCSDRN1')
drContent = drContent.replace('sophiscsn1', 'sophiscsdrn1')
drContent = drContent.replace('SophisISN1', 'SophisISDRN1')
drContent = drContent.replace('SophisASN1', 'SophisASDRN1')
drContent = drContent.replace('SophisCFN1', 'SophisCFDRN1')
drContent = drContent.replace('SophisOSN1', 'SophisOSDRN1')
sourceFile.close()
DRFile = open('input/Common.config', 'w')
DRFile.write(drContent)
DRFile.close()

exit()
