#  Date         Name                      Detail
##==========================================================================================================================
## 20240625     Alvin Mak                 Generate the Risk Fund Historical Price Report for Type-11
#

from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import StringUtil

import cx_Oracle
import datetime
import csv
import os

""" program start """
logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
try:
    today = datetime.date.today()
    logger.info('today = %s', today)

    InputSQLFile = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('InputSQL')
    ReportPath = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportPath')
    ReportFileName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFileName')
    logger.info('InputSQLFile = %s', InputSQLFile)
    logger.info('ReportPath = %s', ReportPath)
    logger.info('ReportFileName = %s', ReportFileName)

    """ format date """
    ReportFileName = StringUtil.formatDate(ReportFileName, today)
    logger.info('ReportFileName = %s', ReportFileName)

    ReportFullOutPath = os.path.join(ReportPath, ReportFileName)
    logger.info('ReportFullOutPath = %s', ReportFullOutPath)

    with open(InputSQLFile, 'r') as SQLFile:
        InputSQL = SQLFile.read().replace('\n', ' ')
        InputSQL = InputSQL.replace('\t', ' ')
        logger.info('InputSQL = %s', InputSQL)
        SQLFile.close()

    sophisDb = SophisDBConnection()
    with cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance()) as dbConn:
        logger.info(dbConn)

        with dbConn.cursor() as cursor:
            cursor.execute(InputSQL)
            results = cursor.fetchall()
            with open(ReportFullOutPath, 'w') as CSVFile:
                writer = csv.writer(CSVFile, delimiter=',', lineterminator="\n", quoting=csv.QUOTE_NONNUMERIC)
                writer.writerow([i[0] for i in cursor.description])  # Header row
                for row in results:
                    writer.writerow(row)
                    logger.info(row)
                if cursor.rowcount > 0:
                    results.close()
                dbConn.close()
                CSVFile.close()

except Exception as e:
    logger.info('%s', str(e))
