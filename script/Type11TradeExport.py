import csv
import logging
import datetime
import xml.etree.ElementTree as ET
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import cx_Oracle

# Get the current date
current_date = datetime.date.today()

# Format the date as YYYYMMDD
business_date = current_date.strftime("%Y%m%d")

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
GivenAllotment = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('allotments')
Header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('ReportHeader')
InputFolder = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('InputFolder')
InputFileName = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('InputFileName')
OutputFolder = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('OuputFolder')
OutputFileName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('OutputFileName')
Type11CustOffTime = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Type11CustOffTime')
ReportHeader = Header.split(',')

xmlFileLocation = f"{InputFolder}{InputFileName}"
xmlFileLocation = xmlFileLocation.replace("{YYYYMMDD}", business_date)
type11TradeOuputFile = f"{OutputFolder}{OutputFileName}"
type11TradeOuputFile = type11TradeOuputFile.replace("{YYYYMMDD}", business_date)
logger.info(f'Xml File Location = {xmlFileLocation}')
logger.info(f'Type 11 File Location = {type11TradeOuputFile}')
logger.info(f'Type11CustOffTime = {Type11CustOffTime}')
tree = ET.parse(xmlFileLocation)
root = tree.getroot()

class Position:
    def __init__(self, sicovam, numOfSecurities, allotment, maturity, instrumentReference, instrumentName):
        self.sicovam = sicovam
        self.numOfSecurities = numOfSecurities
        self.allotment = allotment
        self.maturity = maturity
        self.instrumentReference = instrumentReference
        self.instrumentName = instrumentName

    def __str__(self):
        return f'Sicovam[{self.sicovam}] numOfSecurities[{self.numOfSecurities}] allotment[{self.allotment}] maturity[{self.maturity}]'


x = root.findall('{http://www.sophis.net/reporting}default0/{http://www.sophis.net/reporting}window/{http://www.sophis.net/reporting}line')
counter = 0
cachedPositions = []
for pos in x:
    # print(pos)
    maturity = pos.find('{http://www.sophis.net/reporting}maturity')
    if (maturity is not None):
        maturity_date = datetime.datetime.strptime(maturity.text, "%Y-%m-%d")
        cutover_date = datetime.datetime.strptime(f"{Type11CustOffTime}",  "%Y-%m-%d")
        if ( maturity_date > cutover_date ):
            sicovam = pos.find('{http://www.sophis.net/reporting}instrumentCode')
            numOfSecurities = pos.find('{http://www.sophis.net/reporting}numberOfSecurities')
            allotment = pos.find('{http://www.sophis.net/reporting}allotment')
            instrumentReference = pos.find('{http://www.sophis.net/reporting}instrumentReference')
            instrumentName = pos.find('{http://www.sophis.net/reporting}instrumentName')
            if ( allotment.text in GivenAllotment ):
                p = Position(sicovam.text, numOfSecurities.text, allotment.text, maturity_date, instrumentReference.text, instrumentName.text)
                cachedPositions.append(p)
                counter += 1
for p in cachedPositions:
    logger.info(p)


sophisDb = SophisDBConnection()
logger.info(sophisDb)
with cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                       sophisDb.getHost() + "/" + sophisDb.getDbInstance()) as dbConn:
    with open(SQL) as file: sql = file.read()
    with open(f'{type11TradeOuputFile}', 'w', newline='') as file:
        writer = csv.writer(file)
        with dbConn.cursor() as cursor:
            logger.info(sql)
            writer.writerow(ReportHeader)
            for p in cachedPositions:
                cursor.execute(sql, sicovam=(p.sicovam))
                result = cursor.fetchall()
                for row in result:
                    rowRecord = [p.sicovam, p.allotment, p.maturity.strftime('%Y%m%d'), p.numOfSecurities, p.instrumentReference, p.instrumentName]
                    for x in row:
                        if isinstance(x, datetime.datetime):
                            rowRecord.append(x.strftime('%Y%m%d'))
                        else:
                            rowRecord.append(x)
                logger.info(rowRecord)
                writer.writerow(rowRecord)

