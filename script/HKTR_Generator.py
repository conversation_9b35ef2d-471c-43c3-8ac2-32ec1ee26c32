'''
Date        Name            Detail
========================================================================================================================
20250128    Alvin Mak       Initial version of HKTR trade generator to handle in-scope allotments.
20250708    Charles         Updated and enhanced the parsing logic
'''
import json
import csv
import sys
import argparse
import logging
import datetime

from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from HKTR.product_list import PRODUCTS


def get_date_from_args(args):
    parser = argparse.ArgumentParser(description='Process the optional reportdate argument.')

    # Define positional argument for the config file
    parser.add_argument('config_path', type=str, help='Path of the configuration file.')

    # Define an optional keyword argument for reportdate. Default to None if not provided.
    parser.add_argument('--reportdate', type=str, default=None,
                        help='Report Date in YYYYMMDD format. If it is not provided, today\'s date is used.')

    parsed_args = parser.parse_args(args)

    # Use today's date if no reportdate argument is provided
    if parsed_args.reportdate is None:
        now = datetime.datetime.now()
        return now.strftime('%Y-%m-%d')
    else:
        try:
            return datetime.datetime.strptime(parsed_args.reportdate, '%Y%m%d').strftime('%Y-%m-%d')
        except ValueError:
            logging.error('Invalid date format. Please provide a date in YYYYMMDD format.')
            return None

def generate_csv_report(product_class, class_name, all_positions, report_date, output_directory, file_date):
    try:
        product_instance = product_class()
        items = product_instance.generate_positions(all_positions, report_date)
        logging.info(f'Number of {class_name}={len(items)}')

        filename = f"{output_directory}/{file_date}_HKTR_{class_name.lower().replace(' ', '_')}.csv"
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
            csvWriter.writerow(product_instance.getHeader())
            csvWriter.writerows(items)

        logging.info(f'Successfully generated {filename}')
        return True
    except Exception as e:
        logging.error(f'Failed to generate {class_name} report: {str(e)}')
        return False

if __name__ == "__main__":

    # Get the configuration detail from the arguments.
    if len(sys.argv) > 1:
        args = sys.argv[1:]     # Get the entire namespace of arguments.
        reportDate = get_date_from_args(args)

        if reportDate is not None:
            print(f"Date to process: {reportDate}")
        else:
            logging.info('Failed to retrieve a valid date. Exiting...')
            sys.exit(1)

    dateFormat = "YYYYMMDD"
    fileDate = reportDate.replace("-", "")

    logFileName = PropertiesUtil.configObject.GetLogFileName()
    log = GTJALogging(logFileName)
    logger = log.GetLogger()
    businessDate = datetime.date.today().strftime('%Y%m%d')
    input_file = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('InputFile')
    reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('reportFolder')
    input_file = input_file.replace('{date}', fileDate)

    with open(reportFolder+input_file, "r") as f:
        allPositions = json.load(f)

    output_directory = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('OutputFolder')

    for product_key, product_config in PRODUCTS.items():
        success = generate_csv_report(
            product_config['class'],
            product_config['filename'],
            allPositions,
            reportDate,
            output_directory,
            fileDate
        )
        if not success:
            logging.warning(f'Failed to generate report for {product_key}')

    # accu_decu = accu_decu()
    # items = accu_decu.generate_positions(allPositions, reportDate)
    # logging.info('Number of Accumulators & Decumulators=%d', len(items))
    # with open(f"{output_directory}/{fileDate}_HKTR_accu_decu.csv", 'w', newline='') as csvfile:
    #     csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
    #     csvWriter.writerow(accu_decu.getHeader())
    #     for x in items:
    #         csvWriter.writerow(x)
    #
    # autocall_snball_fcn_swap = autocall_snball_fcn_swap()
    # items = autocall_snball_fcn_swap.generate_positions(allPositions, reportDate)
    # logging.info('Number of Autocall, SnowBall & FCN Swap=%d', len(items))
    # with open(f"{output_directory}/{fileDate}_HKTR_autocall_snball_fcn_swap.csv", 'w', newline='') as csvfile:
    #     csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
    #     csvWriter.writerow(autocall_snball_fcn_swap.getHeader())
    #     for x in items:
    #         csvWriter.writerow(x)
    #
    # ccs_cds_irs = ccs_cds_irs()
    # items = ccs_cds_irs.generate_positions(allPositions, reportDate)
    # logging.info('Number of CCS, CDS & Interest Rate Swap=%d', len(items))
    # with open(f"{output_directory}/{fileDate}_HKTR_ccs_cds_irs.csv", 'w', newline='') as csvfile:
    #     csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
    #     csvWriter.writerow(ccs_cds_irs.getHeader())
    #     for x in items:
    #         csvWriter.writerow(x)
    #
    # equity_futures_swap = equity_futures_swap()
    # items = equity_futures_swap.generate_positions(allPositions, reportDate)
    # logging.info('Number of Equity & Futures Swap=%d', len(items))
    # with open(f"{output_directory}/{fileDate}_HKTR_equity_futures_swap.csv", 'w', newline='') as csvfile:
    #     csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
    #     csvWriter.writerow(equity_futures_swap.getHeader())
    #     for x in items:
    #         csvWriter.writerow(x)
    #
    # fx_spot = fx_spot()
    # items = fx_spot.generate_positions(allPositions, reportDate)
    # logging.info('Number of FX Spot=%d', len(items))
    # with open(f"{output_directory}/{fileDate}_HKTR_fx_spot.csv", 'w', newline='') as csvfile:
    #     csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
    #     csvWriter.writerow(fx_spot.getHeader())
    #     for x in items:
    #         csvWriter.writerow(x)
    #
    # fxforward_ndf = fxforward_ndf()
    # items = fxforward_ndf.generate_positions(allPositions, reportDate)
    # logging.info('Number of FX Forward & NDF=%d', len(items))
    # with open(f"{output_directory}/{fileDate}_HKTR_fxforward_ndf.csv", 'w', newline='') as csvfile:
    #     csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
    #     csvWriter.writerow(fxforward_ndf.getHeader())
    #     for x in items:
    #         csvWriter.writerow(x)
    #
    # index_futures = index_futures()
    # items = index_futures.generate_positions(allPositions, reportDate)
    # logging.info('Number of Index Futures=%d', len(items))
    # with open(f"{output_directory}/{fileDate}_HKTR_index_futures.csv", 'w', newline='') as csvfile:
    #     csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
    #     csvWriter.writerow(index_futures.getHeader())
    #     for x in items:
    #         csvWriter.writerow(x)
    #
    # options = otc_barrier_digital_options()
    # items = options.generate_positions(allPositions, reportDate)
    # logging.info('Number of OTC Options, Barrier Options & Digital Options=%d', len(items))
    # with open(f"{output_directory}/{fileDate}_HKTR_otc_barrier_digital_options.csv", 'w', newline='') as csvfile:
    #     csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
    #     csvWriter.writerow(options.getHeader())
    #     for x in items:
    #         csvWriter.writerow(x)
    #
    # trs_bond = trsbonds_trsbasketbonds()
    # items = trs_bond.generate_positions(allPositions, reportDate)
    # logging.info('Number of TRS Bonds & TRS Bonds Basket=%d', len(items))
    # with open(f"{output_directory}/{fileDate}_HKTR_trsbonds_trsbasketbonds.csv", 'w', newline='') as csvfile:
    #     csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
    #     csvWriter.writerow(trs_bond.getHeader())
    #
    #     for x in items:
    #         csvWriter.writerow(x)
    #
    # trscb_index_swap = trscb_index_swap()
    # items = trscb_index_swap.generate_positions(allPositions, reportDate)
    # logging.info('Number of TRS CB & Index Swap=%d', len(items))
    # with open(f"{output_directory}/{fileDate}_HKTR_trscb_index_swap.csv", 'w', newline='') as csvfile:
    #     csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
    #     csvWriter.writerow(trscb_index_swap.getHeader())
    #
    #     for x in items:
    #         csvWriter.writerow(x)




