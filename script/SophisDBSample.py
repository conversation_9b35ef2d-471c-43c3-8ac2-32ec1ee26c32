from GTJA.SophisDBConnection import SophisDBConnection
from GTJA import PropertiesUtil
from GTJA import GTJALogger
import base64
import cx_Oracle
import datetime


logFileName = PropertiesUtil.configObject.GetLogFileName()
logger = GTJALogger.GTJALogger(logFileName)

#sophisDb = SophisDBConnection('config/sophis.connection.properties')
fileread = open('a.txt', 'r')
c = fileread.read()
print(c)
if c == 'None':
    c = 0
    print(c)

today_str = datetime.date.today().strftime('%Y%m%d')
print(today_str)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(), sophisDb.getHost()+"/"+sophisDb.getDbInstance())
print(dbConn)
cursor = dbConn.cursor()
cursor.execute("""select textdata, errormessage, max(internalref) from rma_messages where internalref > :id and entereddatecode=date_to_num(to_date( :today , 'YYYYMMDD'))""", id=c,today=today_str)
results = cursor.fetchall()
filewrite = open('a.txt', 'w')

print("Records = [", len(results), "]")

if (len(results) > 0 ):
    for internalref in results:
        filewrite.write(str(internalref[0]))
        print("ref ", internalref[0])
else:
    print("No record found!")

