'''
Date        Name            Detail
========================================================================================================================
20250810    Alvin Mak       Initial version of HKTR Valuation report to handle in-scope allotments.
'''

import json
import csv

from GTJA.GTJALogging import GTJALogging
#from HKTR_Valuations.HKTR_valuations_allotments import HKTR_valuations
from GTJA import PropertiesUtil
import logging
import datetime
from HKTR_Valuations.product_list import PRODUCTS


def generate_csv_report(product_class, class_name, all_positions, output_directory, file_date):
    try:
        product_instance = product_class()
        items = product_instance.generate_positions(all_positions)
        logging.info(f'Number of {class_name}={len(items)}')

        filename = f"{output_directory}/{file_date}_HKTR_{class_name.lower().replace(' ', '_')}.csv"
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
            csvWriter.writerow(product_instance.getHeader())
            csvWriter.writerows(items)

        logging.info(f'Successfully generated {filename}')
        return True
    except Exception as e:
        logging.error(f'Failed to generate {class_name} report: {str(e)}')
        return False


if __name__ == "__main__":

    dateFormat = "YYYYMMDD"
    yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
    fileDate = yesterday.strftime('%Y%m%d')

    logFileName = PropertiesUtil.configObject.GetLogFileName()
    log = GTJALogging(logFileName)
    logger = log.GetLogger()
    businessDate = datetime.date.today().strftime('%Y%m%d')
    input_file = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('InputFile')
    reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('reportFolder')
    input_file = input_file.replace('{date}', fileDate)

    with open(reportFolder+input_file, "r") as f:
        allPositions = json.load(f)

    output_directory = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('OutputFolder')

    for product_key, product_config in PRODUCTS.items():
        success = generate_csv_report(
            product_config['class'],
            product_config['filename'],
            allPositions,
            output_directory,
            fileDate
        )
        if not success:
            logging.warning(f'Failed to generate report for {product_key}')
