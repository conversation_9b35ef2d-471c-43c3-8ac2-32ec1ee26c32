import xml.etree.ElementTree as ET

def parseXML(xmlfile):
    tree = ET.parse(xmlfile)
    root = tree.getroot()
    context = ""
    for item in root:
        idx = 0
        for child in item.iter('{http://www.sophis.net/reporting}sqlSourceResult'):
            context += "<tr>"
            context += "<td>"+child.find('{http://www.sophis.net/reporting}VALUE').text+"</td>"
            context += "<td>"+child.find('{http://www.sophis.net/reporting}LIBELLE').text+"</td>"
            context += "<td>"+child.find('{http://www.sophis.net/reporting}DATEDIV').text+"</td>"
            context += "<td>"+child.find('{http://www.sophis.net/reporting}DATEEXDIV').text+"</td>"
            context += "</tr>"
    return context

records = parseXML("a.xml")
count = len(records)
if len(records) > 0:
    print("There are some records")
else:
    print("There is no record")