import cx_Oracle
import configparser
import base64
from .Environment import *

class SophisDBConnection:
    def __init__(self):
        dbFile = self.getPropertiesFolder() + '/' +'sophis_' + getEnvironment() + '.connection.properties'
        print ("database properties: ", dbFile)
        try:
            self.dbConfigFile = dbFile
            self.config = configparser.RawConfigParser()
            self.config.sections()
            self.config.read(self.dbConfigFile)
            self.userId = self.config['SophisDB'].get("User")
            encryptedPassword = self.config['SophisDB'].get("Password")
            print(encryptedPassword)
            self.password = self.__password64Decode(encryptedPassword)
            self.host = self.config['SophisDB'].get("Host")
            self.dbInstance = self.config['SophisDB'].get("Instance")
            print("Oracle Connection Info: ", self.userId, encryptedPassword, self.host, self.dbInstance)
            #print(self.__password64Encode('sophisprd'))
        except KeyError:
            print("Incorrect config file for DB")

    def getPropertiesFolder(self):
        return 'config'

    def getEnv(self, envFile):
        print("Environemnt File: ", envFile)
        environmentConf = configparser.RawConfigParser()
        environmentConf.sections()
        environmentConf.read(envFile)
        env = environmentConf['Sophis'].get('ENV')
        if (env == None):
            return 'dev'
        return env


    def __password64Encode(self, password):
        return base64.b64encode(password.encode('utf-8'))

    def __password64Decode(self, encodedPassword):
        decodedByte = base64.b64decode(encodedPassword)
        return decodedByte.decode('utf-8')

    def getUserId(self):
        return self.userId

    def getPassword(self):
        return self.password

    def getHost(self):
        return self.host

    def getDbInstance(self):
        return self.dbInstance

#sophisCon = SophisDBConnection("sophis.conf")
