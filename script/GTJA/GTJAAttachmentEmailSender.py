#  Date         Name                      Detail
##==========================================================================================================================
# 20240505     Alvin Mak                  To support email delivery includes HTML and attachment.
#

import smtplib
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage
import logging
from GTJA import PropertiesUtil


def sendEmail(content, attachment, attachment_type, today_str, dateFormat):
    logging.info("Try to send email")
    Sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
    Recipient = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Recipient')
    Subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
    SmptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
    SmptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

    logging.info('attachment: ' + attachment)
    logging.info('attachment_type: ' + attachment_type)
    logging.info('today_str: ' + today_str)
    logging.info('dateFormat: ' + dateFormat)

    Subject = Subject.replace(dateFormat, today_str)
    logging.info(Subject)

    msg = EmailMessage()
    msg['From'] = Sender
    msg['To'] = Recipient
    msg['Subject'] = Subject
    msg.set_content(content)
    msg.add_alternative(content, subtype='html')

    try:
        with open(attachment, 'rb') as f:
            file_data = f.read()
        msg.add_attachment(file_data, maintype="application", subtype=attachment_type, filename=attachment)
    except Exception as e:
        logging.info('%s', str(e))

    logging.info("================================================================")
    logging.info(msg)
    if PropertiesUtil.configObject.IsPRD():
        with smtplib.SMTP(SmptHost, SmptPort) as server:
            server.send_message(msg)
    else:
        logging.info("No email sending for DEV or UAT")