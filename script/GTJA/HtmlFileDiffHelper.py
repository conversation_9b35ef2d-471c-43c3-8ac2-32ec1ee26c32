import difflib

class HtmlFileDiffHelper:
    def __init__(self, file1, file2):
        self.__file1 = open(file1).readlines()
        self.__file2 = open(file2).readlines()


    def anyDifference(self):
        differ = difflib.Differ();
        diff_context = differ.compare(self.__file1, self.__file2);
        changes = [l for l in diff_context if l.startswith('+ ') or l.startswith('- ')]
        if ( len(changes) == 0 ):
            return False
        return True

    def createHTMLContent(self, outputFile):
        htmlCreator = difflib.HtmlDiff()
        diffResult = htmlCreator.make_file(self.__file1, self.__file2, context=True, numlines=0)
        with open(outputFile, 'w') as f:
            f.writelines(diffResult)