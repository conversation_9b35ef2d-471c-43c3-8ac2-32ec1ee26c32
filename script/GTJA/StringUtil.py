import datetime
import os

def formatDate(str, date):
    newstr = str.replace('YYYY', date.strftime('%Y'))
    newstr = newstr.replace('YY', date.strftime('%y'))

    newstr = newstr.replace('MMMM', date.strftime('%B'))
    newstr = newstr.replace('MMM', date.strftime('%b'))
    newstr = newstr.replace('MM', date.strftime('%m'))

    newstr = newstr.replace('DD', date.strftime('%d'))
    return newstr

def readSqlFromFile(scriptFile, join_line=1):
    script=''
    with open(scriptFile, 'r') as file:
        script = file.read()
        if(join_line):
            script = script.replace('\n', ' ')
            script = script.replace('\t', ' ')
            script = ' '.join(script.split())
        file.close()
    return script

if __name__ == '__main__':
    tod=datetime.date.today()
    str=formatDate('YYYY-MM-DD',tod)
    print('formatDate(YYYY-MM-DD,tod) = %s' %str)

    str=formatDate('YY-MM-DD',tod)
    print('formatDate(YY-MM-DD,tod) = %s' %str)

    str=formatDate('YY-MMMM-DD',tod)
    print('formatDate(YY-MMMM-DD,tod) = %s' %str)

    str=formatDate('YY-MMM-DD',tod)
    print('formatDate(YY-MMM-DD,tod) = %s' %str)
