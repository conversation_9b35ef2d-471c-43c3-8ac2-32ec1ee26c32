import datetime
from datetime import timedelta
from dateutil.relativedelta import relativedelta

class DateDeltaHandler:
    def __init__(self, currentDate, delta):
        if len(delta) > 0:
            deltaInt = int(delta[:len(delta) - 1])
            deltaType = delta[-1]
            if deltaType == 'D':
                self.__deltaDate__ = currentDate + relativedelta(days = deltaInt)
            elif deltaType == 'M':
                self.__deltaDate__ = currentDate + relativedelta(months = deltaInt)
            elif deltaType == 'Y':
                self.__deltaDate__ = currentDate + relativedelta(years = deltaInt)
            elif deltaType == 'W':
                self.__deltaDate__ = currentDate + relativedelta(weeks = deltaInt)
            else:
                self.__deltaDate__ = currentDate
        else:
            self.__deltaDate__ = currentDate

    def getDeltaDate(self):
        return self.__deltaDate__
