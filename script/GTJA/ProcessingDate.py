import logging
import datetime

class ProcessingDate:
    def __init__(self):
        self._today_ = datetime.date.today()
        Day = self._today_.strftime('%A')
        if Day == 'Monday':
            logging.info("Delta is 3")
            no_of_days = datetime.timedelta(days=3)
        else:
            logging.info("Delta is 1")
            no_of_days = datetime.timedelta(days=1)
        self._lastBusinessDate_ = self._today_ - no_of_days

        logging.info("Today [" + str(self._today_) + "]")
        logging.info("Last Business Date [" + str(self._lastBusinessDate_) + "]")

    def getCurrentDate(self):
        return self._today_

    def getLastBusinessDate(self):
        return self._lastBusinessDate_

    def getCurrentDateInString(self):
        return self._today_.strftime('%Y%m%d')

    def getLastBusinessDateInString(self):
        return self._lastBusinessDate_.strftime('%Y%m%d')