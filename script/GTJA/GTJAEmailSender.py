import smtplib
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage
import logging
from GTJA import PropertiesUtil


def sendEmail(content, today_str, dateFormat, alarm=False):
    logging.info("Try to send email")
    sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
    receiptent = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Receiptent')
    subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
    smptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
    smptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

    subject = subject.replace(dateFormat, today_str)

    subject = subject
    if alarm == False:
        subject = subject + " - OK"
    else:
        subject = subject + " - Alert"
    msg = EmailMessage()
    msg['From'] = sender
    msg['To'] = receiptent
    msg['Subject'] = subject
    msg.set_content(content)
    msg.add_alternative(content, subtype='html')
    logging.info("================================================================")
    logging.info(msg)
    if (PropertiesUtil.configObject.IsPRD()):
        with smtplib.SMTP(smptHost, smptPort) as server:
            server.send_message(msg)
    else:
        logging.info("No email sending for DEV or UAT")