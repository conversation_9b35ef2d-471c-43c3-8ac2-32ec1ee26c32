import logging
import datetime

class GTJALogging:
    def __init__(self, logFile=None, folder=None):
        today_str = datetime.date.today().strftime('%Y%m%d')
        idx = logFile.index('/')
        if ( idx > 0 ):
            folder = logFile[0:idx]
            logFile = logFile[idx+1::]
        if folder == None:
            folder = ".";
        if logFile != None:
            logging.basicConfig(level=logging.DEBUG,
                                format="%(asctime)s %(name)-12.12s [%(threadName)-12.12s] [%(levelname)-5.5s]  %(message)s",
                                handlers=[logging.StreamHandler(),
                                          logging.FileHandler(folder+"/"+today_str+"_"+logFile, mode="a+")])
        else:
            logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(threadName)-12.12s] [%(levelname)-5.5s]  %(message)s", handlers=[logging.StreamHandler()])

    def GetLogger(self):
        return logging.getLogger()
