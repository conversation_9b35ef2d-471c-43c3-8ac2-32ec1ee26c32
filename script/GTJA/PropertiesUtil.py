import configparser
import sys
import datetime
from .Environment import *

CONFIG = "Config="
OUTPUT_PROPERTIES = "Output"
FORMAT_PROPERTIES = "Formatter"
DATE_PATTERN = "YYYYMMDD"

class PropertiesUtil:

	def __init__(self, arguments):
		config_file = ReadArguments(arguments)
		print("[PropertiesUtil] config_file =", config_file)
		if config_file != None:
			self.__config = configparser.RawConfigParser()
			self.__config.sections()
			self.__config.read(config_file)
			self.__date_format = self.GetDateFormat()
			print("Date Format = ", self.__date_format)
			self.__report_date = self.GetToday()
			self.__logfile = self.CreateLogFile()
			#self.__output_file = open(self.__logfile, "w")
		else:
			print("No Configuration file provided, Error exit")

	def GetEnv(self):
		return getEnvironment()

	def IsPRD(self):
		env = getEnvironment()
		return env.lower() == 'prd'

	def GetConfigProperties(self):
		return self.__config

	def GetLogFileName(self):
		return self.__logfile

	def CreateLogFile(self):
		logFolder = self.__config[OUTPUT_PROPERTIES].get('LogFolder')
		print("[PropertiesUtil] Log Folder = ", logFolder)
		logSuffix = self.__config[OUTPUT_PROPERTIES].get('LogFileSuffix')
		logExt = self.__config[OUTPUT_PROPERTIES].get('LogFileExt')
		filename = logFolder + logSuffix + logExt
		filename = filename.replace(DATE_PATTERN, self.__report_date)
		return filename

	def GetDateFormat(self):
		return self.__config[FORMAT_PROPERTIES].get('DateFormat')

	def GetToday(self):
		x = datetime.datetime.now()
		return x.strftime(self.__date_format)

	#def GetOutputFile(self):
	#	return self.__output_file

def ReadArguments(arguments):
	argumentSize = len(arguments) - 1
	print("[PropertiesUtil] number of arguments : ", argumentSize)
	for arg in arguments:
		if arg.startswith(CONFIG):
			return arg[len(CONFIG):]

configObject = PropertiesUtil(sys.argv)
#print (configObject.GetLogFileName())
