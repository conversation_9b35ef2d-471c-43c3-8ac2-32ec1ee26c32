## Date         Name                       Detail
##==========================================================================================================================
##20211101      <PERSON>                 Update the function to accept int type
##
##
import logging

class GTJAFormatter:
    def formatAmount(val, dp=10):
        numVal = val
        rtn = ''
        if ( type(val) == float or type(val) == int or (type(val) == str and len(val) > 0) ):
            if (type(val)==str):
                numVal = float(val)
            if (numVal < 0):
                rtn = "{:,.{}f}".format(abs(numVal), dp)
                rtn = "($" + rtn + ")"
            elif (numVal > 0):
                rtn = "{:,.{}f}".format(numVal, dp)
                rtn = '$'+rtn
            return rtn
        else:
            return '-';

