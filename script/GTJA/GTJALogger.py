import logging

class GTJALogger:
    def __init__(self, logger=None):
        if logger != None:
            print("[GTJALogger] Logger file name = ", logger)
            self.__logger = open(logger, "w")
        else:
            print("[GTJALogger] No logger file define")

    def logging(self, *args, sep=' ', end='\n'):
        if self.__logger != None :
            print(*args, sep=sep, end=end, file=self.__logger)
        print(*args, sep=sep, end=end)

    def close(self):
        self.__logger.close()