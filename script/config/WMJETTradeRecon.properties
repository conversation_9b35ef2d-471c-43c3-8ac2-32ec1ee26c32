[Formatter]
DateFormat=%Y%m%d

[Output]
LogFolder=log/
LogFileSuffix=WMJETTradeRecon
LogFileExt=.txt
ReportFolder=output

[Input]
SQL=SQL/WMJETTradeRecon.sql
DUPLICATE_TRADE_SQL=SQL/WMJETDuplicateTrade.sql
template=template/WMJETTradeRecon.html
JetTradeFile=D:\Sophis\App\GTJA_Reports\WealthManagement\in\jet-sophis-execution_1-YYYYMMDD.csv


[Email]
SMTP_HOST=************
SMTP_PORT=25
Sender=<EMAIL>
Receiptent=<EMAIL>,<EMAIL>
#Receiptent=<EMAIL>
Subject=Sophis Jet Trade Recon Report - YYYYMMDD