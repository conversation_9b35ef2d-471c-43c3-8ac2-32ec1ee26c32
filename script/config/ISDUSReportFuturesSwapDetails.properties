[Formatter]
dateformat = %Y%m%d

[Output]
logfolder = log/
logfilesuffix = ISDUSReportFuturesSwapDetails
logfileext = .txt
ReportPath=D:\Sophis\App\GTJA_Reports\ISD\out\MarkitUSReporting\DailyReport
#ReportPath=output/
ReportExcelFileName=ISDUSReportFuturesSwapDetails.xlsx
ReportHTMLFileName=ISDUSReportFuturesSwapDetails.html

[Input]
SQLRecord = SQL/ISDUSReportFuturesSwapRecord.sql
SQLDetail = SQL/ISDUSReportFuturesSwapThresholdDetail.sql
template = template/ISDUSReportDetails.html
Default_USDCNH_FX = 7.3

[Email]
SMTP_HOST = ************
SMTP_PORT = 25
Sender = <EMAIL>
Recipient = <EMAIL>,<EMAIL>
Subject = ISD US Reporting Futures Swap Details - YYYYMMDD


