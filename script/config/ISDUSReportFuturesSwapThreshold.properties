[Formatter]
dateformat = %Y%m%d

[Output]
logfolder = log/
logfilesuffix = ISDUSReportFuturesSwapThreshold
logfileext = .txt
reportfolder = output

[Input]
sqlrecord = SQL/ISDUSReportFuturesSwapRecord.sql
sqlsum = SQL/ISDUSReportFuturesSwapThresholdSum1.sql
sqldetail = SQL/ISDUSReportFuturesSwapThresholdDetail.sql
template = template/ISDUSReportThreshold.html
NextThresholdConfig = config/ISDUSReportFuturesSwapNextThreshold.properties
Default_USDCNH_FX = 7.3
ThresholdAmt = 8000000000
ThresholdPercent = 25,50,75,80,85,90,95,100

[Email]
smtp_host = ************
smtp_port = 25
sender = <EMAIL>
receiptent = <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
subject = ISD US Reporting Futures Swap Threshold Alert - YYYYMMDD


