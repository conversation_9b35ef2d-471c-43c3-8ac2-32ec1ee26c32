[Formatter]
DateFormat=%Y%m%d

[Input]
InputSQL1=SQL/Daily_ISD_Maturity_Report1.sql
InputSQL2=SQL/Daily_ISD_Maturity_Report2.sql
Template=Daily_ISD_Maturity_Report.html

[Output]
LogFolder=log/
LogFileSuffix=Daily_ISD_Maturity_Report
LogFileExt=.txt
ReportPath=D:\Sophis\App\GTJA_Reports\ISD\out\DailyMaturity\
#ReportPath=output/
ReportExcelFileName=Daily_ISD_Maturity_Report_YYYYMMDD.xlsx
ReportHTMLFileName=Daily_ISD_Maturity_Report_YYYYMMDD.html

[Parameter]
OffsetDay=7

[Email]
# SMTP_HOST=**************
# SMTP_PORT=25
# Sender=<EMAIL>  
# Recipient=<EMAIL>  
SMTP_HOST=************
SMTP_PORT=25
Sender=<EMAIL>
Recipient=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
Subject=Sophis Daily ISD Maturity Report - YYYYMMDD
