[Formatter]
DateFormat=%Y%m%d

[Output]
LogFolder=log/
LogFileSuffix=WMWarrantsFixingReport
LogFileExt=.txt
template=template/WMWarrantsFixingReport.html


[Input]
SQL=SQL/WMWarrantsFixingReport.sql
template=template/WMWarrantsFixingReport.html
deltaFiles=D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_12_WM_EOD_Portfolio_Hedge.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_12_WM_EOD_Portfolio_Listed.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_12_WM_EOD_Portfolio_MHedge.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_12_WM_EOD_Portfolio_MM.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_13_WM_EOD_Portfolio_Hedge.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_13_WM_EOD_Portfolio_Listed.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_13_WM_EOD_Portfolio_MHedge.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_13_WM_EOD_Portfolio_MM.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_14_WM_EOD_Portfolio_Hedge.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_14_WM_EOD_Portfolio_Listed.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_14_WM_EOD_Portfolio_MHedge.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_14_WM_EOD_Portfolio_MM.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_15_WM_EOD_Portfolio_Hedge.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_15_WM_EOD_Portfolio_Listed.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_15_WM_EOD_Portfolio_MHedge.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_15_WM_EOD_Portfolio_MM.csv
Header=Instrument,Underlying Reference,Derivative Type,Strike,Maturity,First Fixing Date,No Of Fixing days left, Quantity,Global Delta,Daily Delta,Global Gamma


[Email]
SMTP_HOST=************
SMTP_PORT=25
Sender=<EMAIL>
Receiptent=<EMAIL>,<EMAIL>
#Receiptent=<EMAIL>
Subject=Sophis Warrants Fixing Report - YYYYMMDD