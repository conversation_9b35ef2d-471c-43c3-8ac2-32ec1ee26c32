[Formatter]
DateFormat=%Y%m%d

[Output]
LogFolder=log/
LogFileSuffix=WMListedOptionSeriesChecker
LogFileExt=.txt
ReportFolder=output

[Input]
SQL=SQL/WMListedOptionSeriesChecker.sql
template=template/WMListedOptionSeriesChecker.html
upperBound=1.6
lowerBound=0.4
maturityFactor=90



[Email]
SMTP_HOST=************
SMTP_PORT=25
Sender=<EMAIL>
Receiptent=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
Subject=Sophis Listed Option Series Checker - YYYYMMDD