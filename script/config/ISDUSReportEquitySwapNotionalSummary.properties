[Formatter]
dateformat = %Y%m%d

[Output]
logfolder = log/
logfilesuffix = ISDUSReportEquitySwapNotionalSummary
logfileext = .txt
reportfolder = output

[Input]
sqlrecord = SQL/ISDUSReportEquitySwapRecord.sql
sqlsum1 = SQL/ISDUSReportEquitySwapThresholdSum1.sql
sqlsum2 = SQL/ISDUSReportEquitySwapThresholdSum2.sql
template1 = template/ISDUSReportNotionalSummary.html
template2 = template/ISDUSReportNotionalCptySummary.html
Default_USDCNH_FX = 7.3
ThresholdAmt = 400000000

[Email]
smtp_host = ************
smtp_port = 25
sender = <EMAIL>
receiptent = <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
subject = ISD US Reporting Equity Swap Notional & Threshold - YYYYMMDD


