[Formatter]
dateformat = %Y%m%d

[Output]
logfolder = log/
logfilesuffix = ISDUSReportFuturesSwapNotionalSummary
logfileext = .txt
reportfolder = output

[Input]
sqlrecord = SQL/ISDUSReportFuturesSwapRecord.sql
sqlsum1 = SQL/ISDUSReportFuturesSwapThresholdSum1.sql
sqlsum2 = SQL/ISDUSReportFuturesSwapThresholdSum2.sql
template1 = template/ISDUSReportNotionalSummary.html
template2 = template/ISDUSReportNotionalCptySummary.html
Default_USDCNH_FX = 7.3
ThresholdAmt = 8000000000

[Email]
smtp_host = ************
smtp_port = 25
sender = <EMAIL>
receiptent = <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
subject = ISD US Reporting Futures Swap Notional & Threshold - YYYYMMDD


