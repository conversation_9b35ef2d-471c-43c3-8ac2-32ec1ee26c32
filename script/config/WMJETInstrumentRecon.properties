[Formatter]
DateFormat=%Y%m%d

[Output]
LogFolder=log/
LogFileSuffix=WMJETInstrumentRecon
LogFileExt=.txt
ReportFolder=output

[Input]
SQL=SQL/WMJETInstrumentRecon_Warrant.sql;SQL/WMJETInstrumentRecon_CBBC.sql
HOLIDAY_SQL=SQL/HongKongHoliday.sql
template=template/WMJETInstrumentRecon.html
JetInstrumentFile=D:\Sophis\App\GTJA_Reports\WealthManagement\in\instruments.csv


[Email]
SMTP_HOST=************
SMTP_PORT=25
Sender=<EMAIL>
Receiptent=<EMAIL>,<EMAIL>
Subject=Sophis Jet Instrumnet Recon Report - YYYYMMDD