[Formatter]
dateformat = %Y%m%d

[Output]
logfolder = log/
logfilesuffix = ISDUSReportEqutiySwapDetails
logfileext = .txt
ReportPath=D:\Sophis\App\GTJA_Reports\ISD\out\MarkitUSReporting\DailyReport
#ReportPath=output/
ReportExcelFileName=ISDUSReportEqutiySwapDetails.xlsx
ReportHTMLFileName=ISDUSReportEqutiySwapDetails.html

[Input]
sqlrecord = SQL/ISDUSReportEquitySwapRecord.sql
sqldetail = SQL/ISDUSReportEquitySwapThresholdDetail.sql
template = template/ISDUSReportDetails.html
Default_USDCNH_FX = 7.3

[Email]
SMTP_HOST = ************
SMTP_PORT = 25
Sender = <EMAIL>
Recipient = <EMAIL>,<EMAIL>
Subject = ISD US Reporting Equity Swap Details - YYYYMMDD


