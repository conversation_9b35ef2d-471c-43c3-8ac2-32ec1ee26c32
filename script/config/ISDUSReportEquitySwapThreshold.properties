[Formatter]
dateformat = %Y%m%d

[Output]
logfolder = log/
logfilesuffix = ISDUSReportEquitySwapThreshold
logfileext = .txt
reportfolder = output

[Input]
sqlrecord = SQL/ISDUSReportEquitySwapRecord.sql
sqlsum = SQL/ISDUSReportEquitySwapThresholdSum1.sql
sqldetail = SQL/ISDUSReportEquitySwapThresholdDetail.sql
template = template/ISDUSReportThreshold.html
NextThresholdConfig = config/ISDUSReportEquitySwapNextThreshold.properties
Default_USDCNH_FX = 7.3
ThresholdAmt = 400000000
ThresholdPercent = 25,50,75,80,85,90,95,100

[Email]
smtp_host = ************
smtp_port = 25
sender = <EMAIL>
receiptent = <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
subject = ISD US Reporting Equity Swap Threshold Alert - YYYYMMDD


