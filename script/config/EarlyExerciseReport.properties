[Formatter]
DateFormat=%Y%m%d

[Output]
LogFolder=log/
LogFileSuffix=EarlyExerciseReport
LogFileExt=.txt
ReportFolder=D:\Sophis\App\GTJA_Reports\WealthManagement\out
ReportFileName=YYYYMMDD_midnight_EarlyExerciseReport.csv

[Input]
SQL=SQL/EarlyExerciseReport.sql
Header=Underlying Reference,Portfolio,Underlying Last Px,Instrument Name,Option Maturity,Strike,Derivative Type Call/Put,Quantity,Option Theo Px,Dividend Sum,Upcoming Dividend Ex-day,Delta
deltaFiles=D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_22_WM_EOD_Portfolio_Hedge.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_22_WM_EOD_Portfolio_Listed.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_22_WM_EOD_Portfolio_MHedge.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_22_WM_EOD_Portfolio_MM.csv;D:\Sophis\App\GTJA_Reports\WealthManagement\out\YYYYMMDD_22_WM_EOD_Portfolio_OTC.csv
template=template/EarlyExerciseReport.html

[Email]
SMTP_HOST=************
SMTP_PORT=25
Sender=<EMAIL>
Receiptent=<EMAIL>,<EMAIL>
Subject=Sophis Early Exercise Report - YYYYMMDD