[Formatter]
DateFormat=%Y%m%d

[Output]
LogFolder=log/
LogFileSuffix=CoCoBondSettingChecking
LogFileExt=.txt
ReportFileName=YYYYMMDD_CoCoBondSettingChecking.html
ReportFileName2=YYYYMMDD_CoCoBondSettingCheckingNone.html

[Input]
SQL=SQL/CoCoBondSettingChecking.sql
template=template/CoCoBondSettingChecking.html
template2=template/CoCoBondSettingCheckingNone.html
CSV=D:\Sophis\App\GTJA_Reports\MiddleOffice\check\bond.csv
CSVBACK=D:\Sophis\App\GTJA_Reports\MiddleOffice\check\backup\

[Email]
SMTP_HOST=************
SMTP_PORT=25
Sender=<EMAIL>
Receiptent=<EMAIL>,<EMAIL>
Subject=SophisCSN1 Fixed to Floating Bond Setting Checking - YYYYMMDD
Template=input\YYYYMMDD_CoCoBondSettingChecking.html
Template2=input\YYYYMMDD_CoCoBondSettingChecking-None.html