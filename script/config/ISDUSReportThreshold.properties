[Formatter]
dateformat = %Y%m%d

[Output]
logfolder = log/
logfilesuffix = ISDUSReportThreshold
logfileext = .txt
reportfolder = output

[Input]
sqlsum = SQL/ISDUSReportThresholdSum.sql
sqldetail = SQL/ISDUSReportThresholdDetail.sql
template = template/ISDUSReportThreshold.html
NextThresholdConfig = config/ISDUSReportNextThreshold.properties
Default_USDCNH_FX = 6.85
ThresholdAmt = 400000000
ThresholdPercent = 25,50,75,80,85,90,95,100

[Email]
smtp_host = ************
smtp_port = 25
sender = <EMAIL>
receiptent = <EMAIL>,<EMAIL>
subject = ISD US Reporting Threshold Alert - YYYYMMDD


