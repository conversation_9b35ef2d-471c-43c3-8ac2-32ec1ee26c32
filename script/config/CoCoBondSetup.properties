[Formatter]
DateFormat=%Y%m%d

[Output]
LogFolder=log/
LogFileSuffix=CoCoBondSetup
LogFileExt=.txt
ReportFileName=YYYYMMDD_CoCoBondSetup.html

[Input]
SQL=SQL/CoCoBondSetup.sql
SQL2=SQL/OutstandingBondSetup.sql
template=template/CoCoBondSetup.html
CSV=D:\Sophis\App\GTJA_Reports\MiddleOffice\in\bond.csv
CSVBACK=D:\Sophis\App\GTJA_Reports\MiddleOffice\in\backup\

[Email]
SMTP_HOST=************
SMTP_PORT=25
Sender=<EMAIL>
Receiptent=<EMAIL>,<EMAIL>
Subject=SophisCSN1 Fixed to Floating Bond SQL Executed- YYYYMMDD
Template=input\YYYYMMDD_CoCoBondSetup.html