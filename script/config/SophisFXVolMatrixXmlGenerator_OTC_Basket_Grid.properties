[Formatter]
DateFormat=%Y%m%d

[Output]
LogFolder=log/
LogFileSuffix=SophisFXVolMatrixXmlGenerator
LogFileExt=.txt

[Others]
BusinessDate=

[Scripts]
BucketName=Vol Buckets (Fix Dates)
OutputFileName=D:\Sophis\App\GTJA_Reports\WealthManagement\xml\FXVolMatrixMaturity_OTC_Basket_Grid.xml

[FXParam]
Perimeter=<scenario:perimeter><folio:extraction xsi:type="folio:Criteria"><folio:name>WM: FXVolMatrixMaturity (OTC-Basket)</folio:name><folio:filteredDeals>NotLoaded</folio:filteredDeals><folio:criteriumList folio:fillCashAccounts="false" folio:filterUndefined="false" folio:hierarchic="true" folio:includeAllPortfolios="false" folio:keepPositionID="true" folio:lookthrough="0" folio:unique="false"><folio:criterium>Portfolio</folio:criterium></folio:criteriumList><folio:folders folio:excludeEntryPoint="false"><folio:folder xsi:type="folio:StandardPortfolio"><folio:underlyer><instrument:sophis>********</instrument:sophis><instrument:reference instrument:modifiable="UniqueNotPrioritary" instrument:name="Reference">1HKD</instrument:reference></folio:underlyer><folio:id>14773</folio:id></folio:folder><folio:folder xsi:type="folio:StandardPortfolio"><folio:underlyer><instrument:sophis>********</instrument:sophis><instrument:reference instrument:modifiable="UniqueNotPrioritary" instrument:name="Reference">1HKD</instrument:reference></folio:underlyer><folio:id>14774</folio:id></folio:folder></folio:folders></folio:extraction><scenario:portfolio xsi:type="folio:CriteriaPortfolio"><folio:underlyer><instrument:sophis>********</instrument:sophis><instrument:reference instrument:modifiable="UniqueNotPrioritary" instrument:name="Reference">1HKD</instrument:reference></folio:underlyer></scenario:portfolio></scenario:perimeter>
CalculationMode=Distributed
DetailedResults=false
OverVolatility=0.01
BumpMarketPlot=false
CalculateVolga=false
CalculateReserve=false
ResultOutputfile=..\GTJA_Reports\WealthManagement\out\FXVolMatrixMaturity_OTC_Basket_Grid.xml