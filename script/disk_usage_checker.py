from GTJA import PropertiesUtil
from GTJA import GTJALogger
import shutil


throttle = 30
DISK_USAGE 		= "DiskUsage"
LIST_OF_DISK 	= "Harddisk"
PASS 			= "OK"
FAIL 			= "FAILED"

def GetFreeSpacePercentage(path):
	total, used, free = shutil.disk_usage(path)
	return (free/total) * 100

def GetDiskspaceInfo(letter, throttle, logger):
	size = GetFreeSpacePercentage(letter)
	display = "{0:.4f}"
	v = display.format(size)
	if size > throttle:
		result = PASS
	else:
		result = FAIL
	show = display.format(size)
	return logger.logging(letter, " ", show, "% of free space|", result, " (", show, "% free)", sep='')

def CheckDiskUsage(list, throttle, output):
	for disk in list:
		GetDiskspaceInfo(disk, throttle, output)

def Exit():
	logger.close()

logFileName = PropertiesUtil.configObject.GetLogFileName()
logger = GTJALogger.GTJALogger(logFileName)

diskList = PropertiesUtil.configObject.GetConfigProperties()[DISK_USAGE].get(LIST_OF_DISK)
logger.logging("==== Start checking the disk usage ====")
CheckDiskUsage(diskList.split(","), throttle, logger)
Exit()
