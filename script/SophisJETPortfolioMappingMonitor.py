from GTJA import PropertiesUtil
import datetime
import smtplib
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage

sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
receiptent = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Receiptent')
subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
body = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Body')
template = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Template')
smptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
smptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

today_str = datetime.date.today().strftime('%Y%m%d')

dateFormat = "YYYYMMDD"

subject = subject.replace(dateFormat, today_str)
template = template.replace(dateFormat, today_str)

with open(template) as file: content = file.read()

if content.find('Unmatch') > 0:
	subject = subject + " - Failed"
	msg = EmailMessage()
	msg['From'] = sender
	msg['To'] = receiptent
	msg['Subject'] = subject
	msg.set_content(content)
	msg.add_alternative(content, subtype='html')
	with smtplib.SMTP(smptHost, smptPort) as server:
		server.send_message(msg)
else:
	subject = subject + " - OK"
	msg = EmailMessage()
	msg['From'] = sender
	msg['To'] = '<EMAIL>'
	msg['Subject'] = subject
	msg.set_content(content)
	msg.add_alternative(content, subtype='html')
	with smtplib.SMTP(smptHost, smptPort) as server:
		server.send_message(msg)

