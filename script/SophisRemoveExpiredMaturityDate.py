from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import StringUtil

import cx_Oracle
import datetime
import sys
import os
import ast

""" program start """
logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
try:
    """ set today """
    today = datetime.date.today()

    """ read parameters """
    DeleteScriptFile = PropertiesUtil.configObject.GetConfigProperties()['Scripts'].get('DeleteScript')
    BackupScriptFile = PropertiesUtil.configObject.GetConfigProperties()['Scripts'].get('BackupScript')
    BackupScriptOutPath = PropertiesUtil.configObject.GetConfigProperties()['Scripts'].get('BackupScriptPath')
    BackupScriptName = PropertiesUtil.configObject.GetConfigProperties()['Scripts'].get('BackupScriptName')
    BucketNames = PropertiesUtil.configObject.GetConfigProperties()['Scripts'].get('BucketName')
    BusinessDate = PropertiesUtil.configObject.GetConfigProperties()['Others'].get('BusinessDate')

    logger.info('BackupScriptFile = %s', BackupScriptFile)
    logger.info('DeleteScriptFile = %s', DeleteScriptFile)
    logger.info('BackupScriptOutPath = %s', BackupScriptOutPath)
    logger.info('BackupScriptName = %s', BackupScriptName)
    logger.info('BucketName = %s', BucketNames)
    BucketNameList = ast.literal_eval(BucketNames)
    logger.info('BusinessDate = %s', BusinessDate)

    """ format date """
    if BusinessDate != '':
        today = datetime.datetime.strptime(BusinessDate, '%Y%m%d')
        logger.info('Set today = %s', BusinessDate)

    BackupScriptName = StringUtil.formatDate(BackupScriptName, today)
    BackupScriptOutPath = StringUtil.formatDate(BackupScriptOutPath, today)
    BackupScriptFullOutPath = os.path.join(BackupScriptOutPath, BackupScriptName)
    logger.info('BackupScriptFullOutPath = %s', BackupScriptFullOutPath)

    if os.path.exists(BackupScriptFullOutPath):
        os.remove(BackupScriptFullOutPath)

    """ read script """
    with open(BackupScriptFile, 'r') as file:
        BackupScript = file.read().replace('\n', ' ')
        BackupScript = BackupScript.replace('\t', ' ')
        BackupScript = ' '.join(BackupScript.split())
        logger.info('BackupScript = %s', BackupScript)
        file.close()

    with open(DeleteScriptFile, 'r') as file:
        DeleteScript = file.read().replace('\n', ' ')
        DeleteScript = DeleteScript.replace('\t', ' ')
        DeleteScript = ' '.join(DeleteScript.split())
        logger.info('DeleteScript = %s', DeleteScript)
        file.close()

    """ connection to the database """
    sophisDb = SophisDBConnection()
    with cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance()) as dbConn:
        logger.info(dbConn)

        with dbConn.cursor() as cursor:
            for BucketName in BucketNameList:
                cursor.execute(BackupScript, BucketName=BucketName, business_date=today)
                logger.info('Backup %s' % BucketName)
                results = cursor.fetchall()
                """ write backup script result to a file"""
                if cursor.rowcount > 0:
                    with open(BackupScriptFullOutPath, "a") as backupScriptFile:
                        for result in results:
                            logger.info(result[0])
                            backupScriptFile.write(result[0] + '\n')
                        backupScriptFile.close()

        """ delete record from database """
        with dbConn.cursor() as cursor:
            for BucketName in BucketNameList:
                cursor.execute(DeleteScript, BucketName=BucketName, business_date=today)
                logger.info(results)
                dbConn.commit()
                logger.info('Record removed. %s row affected', cursor.rowcount)

except Exception as e:
    logger.info('%s', str(e))
