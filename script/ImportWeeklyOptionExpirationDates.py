import logging
import pyodbc
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import csv
from datetime import datetime

dateFormat = "YYYYMMDD"
logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)

server = PropertiesUtil.configObject.GetConfigProperties()['DB'].get('SERVER')
database = PropertiesUtil.configObject.GetConfigProperties()['DB'].get('DATABASE')
userId = PropertiesUtil.configObject.GetConfigProperties()['DB'].get('UID')
password = PropertiesUtil.configObject.GetConfigProperties()['DB'].get('PWD')
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
csvFile = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('InputFile')


today_str = datetime.today().strftime('%Y%m%d')
csvFile = csvFile.replace('YYYYMMDD', today_str)

# Define the connection string
conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER="+server+";"
    "DATABASE="+database+";"
    "UID="+userId+";"
    "PWD="+password
)

with open(SQL) as file: insert_sql = file.read()


# Establish the connection
conn = pyodbc.connect(conn_str)

cursor = conn.cursor()
maturity_dates = []
# Define the values to insert (list of maturity dates)

with open(csvFile, mode='r', newline='', encoding='utf-8') as csvfile:
    csv_reader = csv.reader(csvfile)
    # Iterate through each row in the CSV
    firstRow = True
    for row in csv_reader:
        logging.info(row)
        if ( len(row)>0):
            if firstRow:
                firstRow = False
            else:
                maturity_dates.append(row)

logging.info(maturity_dates)

formatted_dates = [
    (datetime.strptime(date[0], '%Y-%m-%d').strftime('%Y%m%d'),) for date in maturity_dates
]
logging.info(maturity_dates)

try:
    # Establish the connection using a context manager
    if (len(maturity_dates) > 0):
        with pyodbc.connect(conn_str) as conn:
            delete_script = "delete from SOP_OptionsCalender"
            conn.execute(delete_script)
            with conn.cursor() as cursor:
                # Execute the SQL statement for each maturity date
                cursor.executemany(insert_sql, formatted_dates)
                # Commit the transaction
                conn.commit()
                logging.info("Insert successful for multiple values.")

except pyodbc.Error as e:
    logging.error(f"Database error: {e}")
except Exception as e:
    logging.error(f"An error occurred: {e}")