import json
import csv

from GTJA.GTJALogging import GTJALogging
from TriResolve.interest_rate_swap import irs
from TriResolve.trs_bonds import trs_bonds
from TriResolve.equity_swap import equity_swap
from TriResolve.otc_option import otc_option
from TriResolve.cross_currency_swap import ccs
from TriResolve.fx_forward import fx_forward
from GTJA import PropertiesUtil
import logging
import datetime

if __name__ == "__main__":

    dateFormat = "YYYYMMDD"

    logFileName = PropertiesUtil.configObject.GetLogFileName()
    log = GTJALogging(logFileName)
    logger = log.GetLogger()
    businessDate = datetime.date.today().strftime('%Y%m%d')
    input_file = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('InputFile')
    reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('reportFolder')
    input_file = input_file.replace('{date}', businessDate)

    with open(reportFolder+input_file, "r") as f:
        allPositions = json.load(f)

    output_directory = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('OutputFolder')
    eq = equity_swap()
    items = eq.generate_positions(allPositions)
    logging.info('Number of Equity Swap=%d', len(items))
    with open(output_directory + '/' +businessDate+ '_equity_swap.csv', 'w', newline='') as csvfile:
        csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
        csvWriter.writerow(eq.getHeader())

        for x in items:
            csvWriter.writerow(x)

    trsBond = trs_bonds()
    items = trsBond.generate_positions(allPositions)
    logging.info('Number of TRS Bonds=%d', len(items))
    with open(output_directory + '/' +businessDate+ '_trs_bonds.csv', 'w', newline='') as csvfile:
        csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
        csvWriter.writerow(trsBond.getHeader())

        for x in items:
            csvWriter.writerow(x)

    irs = irs()
    items = irs.generate_positions(allPositions)
    logging.info('Number of Interest Rate Swap=%d', len(items))
    with open(output_directory + '/' +businessDate+ '_interest_rate_swap.csv', 'w', newline='') as csvfile:
        csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
        csvWriter.writerow(irs.getHeader())
        for x in items:
            csvWriter.writerow(x)

    otc_option = otc_option()
    items = otc_option.generate_positions(allPositions)
    logging.info('Number of OTC Options=%d', len(items))
    with open(output_directory + '/' +businessDate+ '_otc_option.csv', 'w', newline='') as csvfile:
        csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
        csvWriter.writerow(otc_option.getHeader())
        for x in items:
            csvWriter.writerow(x)

    ccs = ccs()
    items = ccs.generate_positions(allPositions)
    logging.info('Number of Cross Currency Swap=%d', len(items))
    with open(output_directory + '/' +businessDate+ '_cross_currency_swap.csv', 'w', newline='') as csvfile:
        csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
        csvWriter.writerow(ccs.getHeader())
        for x in items:
            csvWriter.writerow(x)

    fxforward = fx_forward()
    items = fxforward.generate_positions(allPositions)
    logging.info('Number of FX Forward=%d', len(items))
    with open(output_directory + '/' +businessDate+ '_fxforward.csv', 'w', newline='') as csvfile:
        csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
        csvWriter.writerow(fxforward.getHeader())
        for x in items:
            csvWriter.writerow(x)