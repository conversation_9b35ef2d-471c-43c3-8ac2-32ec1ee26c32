#
# Date          Name                    Detail
#==============================================================================================================
#20210118       <PERSON>              Add Instrument Reference

from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import cx_Oracle
import datetime
import smtplib
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage

dateFormat = "YYYYMMDD"

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()


def sendEmail(content, today_str):
    logger.info("Try to send email")
    sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
    receiptent = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Receiptent')
    subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
    body = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Body')
    smptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
    smptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

    subject = subject.replace(dateFormat, today_str)

    subject = subject + " - ALERT"
    msg = EmailMessage()
    msg['From'] = sender
    msg['To'] = receiptent
    msg['Subject'] = subject
    msg.set_content(content)
    msg.add_alternative(content, subtype='html')
    logger.info("================================================================")
    logger.info(msg)
    if (PropertiesUtil.configObject.IsPRD()):
        with smtplib.SMTP(smptHost, smptPort) as server:
            server.send_message(msg)
    else:
        logger.info("No email sending for DEV or UAT")


# with open ('config/sophis.connection.properties') as file: content = file.read()
# print(content)
internalRefFile = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('interalref')
today_str = datetime.date.today().strftime('%Y%m%d')
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')

textfile=open(internalRefFile, 'r')
c=textfile.read()
if c == 'None':
    c = 0
logger.info(c)


today_str = datetime.date.today().strftime('%Y%m%d')
logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', today_str)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(), sophisDb.getHost()+"/"+sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()
with open(SQL) as file: sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql, id=c,today=today_str)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))


records = ""
if (len(results) > 0 ):
    mark = False
    for internalref in results:
        if (mark == False):
            writefile = open(internalRefFile, 'w')
            writefile.write(str(internalref[0]))
            mark = True

        records += "<tr>"
        records += "<td>" + str(internalref[0]) + "</td>"
        records += "<td>" + str(internalref[1]) + "</td>"
        records += "<td>" + str(internalref[2]) + "</td>"
        if(internalref[3] == None):
            records += "<td></td>"
        else:
            records += "<td>" + str(internalref[3]) + "</td>"

        records += "</tr>"
    logger.info(records)
    with open(template) as file:
        templateContent = file.read()
    content = templateContent.replace('###Template###', records)
    outputfile = open('output/JETFailureCheck.html', 'w')
    outputfile.write(content)
    outputfile.close()
    sendEmail(content, today_str)


else:
   logger.info("No record found!")

