## Date         Name                       Detail
# This is a python report for the MO to check the imported TOMS trade in <PERSON><PERSON>s
# They would like to extract the trades from TOMS to Sophis if the coupon date is in between of trade date and value date.
#
#==========================================================================================================================
# 20210617      Chris Chiu                 First version
# 20210628      Chris Chiu                 Send email even there is no record.
#
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJAFormatter import GTJAFormatter
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import cx_Oracle
import datetime
from GTJA import GTJAEmailSender

dateFormat = "YYYYMMDD"
decimal_point = 2

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Header')
businessDate = datetime.date.today().strftime('%Y%m%d')

#Report file
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
outputFilePrefix = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('LogFileSuffix')

#today_str = datetime.date.today().strftime('%Y%m%d')
logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', businessDate)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()
with open(SQL) as file: sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))

outputReport = ''

if (len(results) > 0):
    for internalref in results:
        tradeId = internalref[0]
        instrumentId = internalref[1]
        instrumentName = internalref[2]
        tradeDate = internalref[3]
        valueDate = internalref[4]
        remark = internalref[5]
        couponDate = internalref[6]
        outputReport += '<tr>'
        outputReport += '<td>' + str(tradeId) + '</td>'
        outputReport += '<td>' + str(instrumentId) + '</td>'
        outputReport += '<td>' + instrumentName + '</td>'
        outputReport += '<td>' + tradeDate.strftime('%d-%b-%Y') + '</td>'
        outputReport += '<td>' + valueDate.strftime('%d-%b-%Y') + '</td>'
        outputReport += '<td>' + remark + '</td>'
        outputReport += '<td>' + couponDate.strftime('%d-%b-%Y') + '</td>'
        outputReport += '</tr>'
else:
    outputReport +='<tr> <td> No Record </td></tr>'

with open(template) as file:
    templateContent = file.read()
    content = templateContent.replace('###Template###', outputReport)
    outputfile = open('output/'+outputFilePrefix+'.html', 'w')
    logger.info(content)
    outputfile.write(content)
    outputfile.close()
    GTJAEmailSender.sendEmail(content, businessDate, dateFormat)


