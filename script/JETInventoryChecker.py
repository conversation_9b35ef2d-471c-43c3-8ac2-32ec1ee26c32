from GTJA import GTJALogger
from GTJA import PropertiesUtil
from GTJA.HtmlFileDiffHelper import HtmlFileDiffHelper
import datetime
import smtplib
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage

dateFormat = "YYYYMMDD"

def sendEmail(contentFile, today_str):
    logger.logging("Try to send email")
    sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
    receiptent = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Receiptent')
    subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
    body = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Body')
    template = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Template')
    smptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
    smptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

    subject = subject.replace(dateFormat, today_str)

    with open(contentFile) as file: content = file.read()

    subject = subject + " - ALERT"
    msg = EmailMessage()
    msg['From'] = sender
    msg['To'] = receiptent
    msg['Subject'] = subject
    msg.set_content(content)
    msg.add_alternative(content, subtype='html')
    logger.logging("================================================================")
    logger.logging(msg)
    with smtplib.SMTP(smptHost, smptPort) as server:
        server.send_message(msg)

logFileName = PropertiesUtil.configObject.GetLogFileName()
logger = GTJALogger.GTJALogger(logFileName)

jetFile1 = PropertiesUtil.configObject.GetConfigProperties()['JETInventory'].get('JetFile1')
jetFile2 = PropertiesUtil.configObject.GetConfigProperties()['JETInventory'].get('JetFile2')

today_str = datetime.date.today().strftime('%Y%m%d')

jetFile1 = jetFile1.replace(dateFormat, today_str)
jetFile2 = jetFile2.replace(dateFormat, today_str)

logger.logging("File 1 = ", jetFile1)
logger.logging("File 2 = ", jetFile2)

if ( len(jetFile1) > 0 and len(jetFile2) > 0):
    htmlFileDiff = HtmlFileDiffHelper(jetFile1, jetFile2)
    reportName = today_str + "_JETInventory.html"
    outputReportFullPath = 'output/' + reportName
    htmlFileDiff.createHTMLContent(outputReportFullPath)

    if ( htmlFileDiff.anyDifference() ):
        print("Preparing for alert email ")
        sendEmail(outputReportFullPath, today_str)
    else:
        logger.logging("No change compare with last night report")