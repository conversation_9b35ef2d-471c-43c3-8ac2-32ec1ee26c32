## Date         Name                       Detail
# Default class of the BCOL uploader parser.
#
# ==========================================================================================================================
# 20240920     Alvin Mak                 Initial Version
#

class default_product(object):
    def __init__(self):
        self.__header__ = list()
        self.__csvRecords__ = list()
        pass

    def generate_positions(self, *positions):
        pass

    def getHeader(self):
        return self.__header__

    def getHeaderConfig(self):
        return self.__csvRecords__

    def getFooterConfig(self):
        self.__csvRecords__
