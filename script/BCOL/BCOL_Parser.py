## Date         Name                       Detail
# To generate the Bloomberg BCOL (Accumulator & Decumulator) upload file.
#
# ==========================================================================================================================
# 20240920     Alvin Mak                 Initial Version
#

from .default_product import default_product
from datetime import datetime
from datetime import timedelta, date
from GTJA import PropertiesUtil
from GTJA.GTJALogging import GTJALogging
import logging

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
portfolio = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Portfolio')
logging.info('portfolio = %s', portfolio)
#counterparty = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Counterparty')
#counterpartyList = list(counterparty.split(","))
#guaranteeEnddateOffset = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Guarantee_Enddate_Offset')

class BCOL_Parser(default_product):
    def __init__(self):
        self.__allotment__ = ['Accumulators', 'Decumulators']
        self.__csvRecords__ = list()
        self.__header__ = list()
        self.__header__.append('PORTFOLIO_NAME')
        self.__header__.append('PORTFOLIO_ACTION')
        self.__header__.append('PORTFOLIO_POSITION')
        self.__header__.append('PORTFOLIO_COST_DATE')
        self.__header__.append('PORTFOLIO_COST_PRICE')
        self.__header__.append('PORTFOLIO_CUSTOM_ID')
        self.__header__.append('DLIB_ACTION')
        self.__header__.append('DLIB_DEALID')
        self.__header__.append('BLAN_TEMPLATE')
        self.__header__.append('Broker')
        self.__header__.append('Counterparty')
        self.__header__.append('Notes')
        self.__header__.append('OTCTicker')
        self.__header__.append('TradeDate')
        self.__header__.append('Privilege')
        self.__header__.append('Model')
        self.__header__.append('Paths')
        self.__header__.append('cumulation_type')
        self.__header__.append('direction')
        self.__header__.append('currency')
        self.__header__.append('notional')
        self.__header__.append('effective_date')
        self.__header__.append('maturity_date')
        self.__header__.append('guarantee_end_date')
        self.__header__.append('underlying')
        self.__header__.append('strike')
        self.__header__.append('knock_out_barrier')
        self.__header__.append('forward_strike')
        self.__header__.append('shares')
        self.__header__.append('cumulation_frequency')
        self.__header__.append('payment_frequency')
        self.__header__.append('cumulate_on_ko')
        self.__header__.append('leverage')
        self.__header__.append('shift_convention')
        self.__header__.append('roll_convention')
        self.__header__.append('payment_delay')

    def getHeader(self):
        self.__csvRecords__.append(self.__header__)

    def getHeaderConfig(self):
        header = list()
        header.append(['CONFIGURATION_BEGIN'])
        header.append(['ProcessingMode','PORTFOLIO_REFRESH'])
        header.append(['CONFIGURATION_END'])
        header.append('')
        for rec in header:
            self.__csvRecords__.append(rec)

    def getFooterConfig(self):
        footer = list()
        footer.append(['', '', '', '', '', '', '', '', 'BLT_TABLE', 'fixing_calendar'])
        footer.append(['', '', '', '', '', '', '', '', '', 'UNUSED'])
        footer.append(['', '', '', '', '', '', '', '', '', 'HK'])
        for rec in footer:
            self.__csvRecords__.append(rec)

    def date_convertor(self, date):
        return datetime.strptime(date, '%Y-%m-%d').strftime('%m/%d/%Y')

    def generate_positions(self, positions):
        # Set the pre-defined BCOL header text
        self.getHeaderConfig()
        # Set the BCOL column header field name
        self.getHeader()
        # Loop through all the non-matured positions
        for position in positions:
            if position['instrumentDetail']['allotment'] in self.__allotment__:
                csvRecord = list()
                if (datetime.strptime(position['endDate']['date'], '%Y-%m-%d') > datetime.now() ):
                    try:
                        #PORTFOLIO_NAME
                        csvRecord.append(portfolio)
                        #PORTFOLIO_ACTION
                        csvRecord.append('ADD')
                        #PORTFOLIO_POSITION
                        csvRecord.append('1')
                        #PORTFOLIO_COST_DATE
                        csvRecord.append('')
                        #PORTFOLIO_COST_PRICE
                        csvRecord.append('')
                        #PORTFOLIO_CUSTOM_ID
                        csvRecord.append(position['instrumentDetail']['references']['GTJAStatementInCode'])
                        #DLIB_ACTION
                        csvRecord.append('ADD')
                        #DLIB_DEALID
                        csvRecord.append('')
                        #BLAN_TEMPLATE
                        csvRecord.append('BLT1178')
                        #Broker
                        csvRecord.append('')
                        # Counterparty - Add 'WM' as prefix to the pre-defined counterparties
                        counterparty = position['counterparty']['reference']
                        counterparty = 'WM' + counterparty
                        csvRecord.append(counterparty)
                        logging.info('counterparty = %s', counterparty)
                        #Notes
                        csvRecord.append('')
                        #OTCTicker
                        csvRecord.append('')
                        #TradeDate - Obtain the value from list
                        tradeDate = self.date_convertor(position['TradeDetails'][0]['tradeDate']['date'])
                        csvRecord.append(tradeDate)
                        #Privilege
                        csvRecord.append('Firm')
                        #Model
                        csvRecord.append('Local Volatility')
                        #Paths
                        csvRecord.append('100000')
                        #cumulation_type
                        allotment = position['instrumentDetail']['allotment']
                        if allotment == 'Accumulators':
                            cumulation_type = 'Accumulator'
                        elif allotment == 'Decumulators':
                            cumulation_type = 'Decumulator'
                        else:
                            cumulation_type = allotment
                        logging.info('cumulation_type = %s', cumulation_type)
                        csvRecord.append(cumulation_type)
                        #direction
                        csvRecord.append('Long')
                        #currency
                        csvRecord.append(position['instrumentDetail']['productCurrency']['ISO'])
                        #notional
                        spotPx = position['instrumentDetail']['references']['GTJASpotPx']
                        shares = abs(position['TradeDetails'][0]['quantity'])
                        fixingClauseCount = position['FixingClauseCount']
                        notional = round(float(spotPx) * float(shares) * float(fixingClauseCount - 1), 2)
                        csvRecord.append(notional)
                        #effective_date
                        effective_date = self.date_convertor(position['IssueDate']['date'])
                        csvRecord.append(effective_date)
                        #maturity_date
                        maturity_date = self.date_convertor(position['endDate']['date'])
                        csvRecord.append(maturity_date)
                        #guarantee_end_date
                        guarantee_end_date = position['FixingDate']
                        if guarantee_end_date != '':
                            guarantee_end_date = self.date_convertor(guarantee_end_date)
                        logging.info('guarantee_end_date = %s', guarantee_end_date)
                        csvRecord.append(guarantee_end_date)
                        #underlying
                        csvRecord.append(position['ClauseUnderlying']['references']['TICKER'])
                        #strike
                        spotPx = position['instrumentDetail']['references']['GTJASpotPx']
                        #strikePercent = position['instrumentDetail']['references']['GTJAStrikePercent']
                        strike = round(float(spotPx), 4)
                        logging.info('strike = %s', strike)
                        csvRecord.append(strike)
                        #knock_out_barrier
                        csvRecord.append(position['instrumentDetail']['references']['GTJAKOPercent'])
                        #forward_strike
                        csvRecord.append(position['instrumentDetail']['references']['GTJAStrikePercent'])
                        #shares - Obtain the value from list
                        shares = abs(position['TradeDetails'][0]['quantity'])
                        csvRecord.append(shares)
                        #cumulation_frequency
                        csvRecord.append('Daily')
                        #payment_frequency
                        csvRecord.append('Biweekly')
                        #cumulate_on_ko
                        csvRecord.append('FALSE')
                        #leverage - Loop through the list to find out Leverage
                        clauseName = [d['ClauseName'] for d in position['PayOffs']]
                        i = 0
                        for clauseItem in clauseName:
                            if clauseItem == 'M_eLeverage':
                                leverage = position['PayOffs'][i]['Value']
                                break
                            else:
                                i += 1
                        logging.info('leverage = %s', leverage)
                        csvRecord.append(leverage)
                        #shift_convention
                        csvRecord.append('ModifiedFollowing')
                        #roll_convention
                        csvRecord.append('Backward')
                        #payment_delay
                        csvRecord.append('')

                    except Exception as err:
                        csvRecord.append('<<<'+str(err)+'>>>')
                    self.__csvRecords__.append(csvRecord)
        # Set the pre-defined BCOL footer text
        self.getFooterConfig()

        return self.__csvRecords__