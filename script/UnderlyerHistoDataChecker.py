##
# files:
#   UnderlyerHistoDataChecker.py
#   UnderlyerHistoDataChecker.sql
#   UnderlyerHistoDataChecker.properties
#   UnderlyerHistoDataChecker.html

# Date         Name                       Detail
#==========================================================================================================================
# 20200605     Chris <PERSON>                 Check if there is any missing historical market data in Sophis
#
#
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import cx_Oracle
import datetime
import smtplib
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage

dateFormat = "YYYYMMDD"

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()


def sendEmail(content, generationDate):
    logger.info("Try to send email")
    sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
    receiptent = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Receiptent')
    subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
    body = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Body')
    smptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
    smptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

    subject = subject.replace(dateFormat, generationDate)

    subject = subject + " - ALERT"
    msg = EmailMessage()
    msg['From'] = sender
    msg['To'] = receiptent
    msg['Subject'] = subject
    msg.set_content(content)
    msg.add_alternative(content, subtype='html')
    logger.info("================================================================")
    logger.info(msg)
    if (PropertiesUtil.configObject.IsPRD()):
        with smtplib.SMTP(smptHost, smptPort) as server:
            server.send_message(msg)
    else:
        logger.info("No email sending for DEV or UAT")



today_str = datetime.date.today().strftime('%Y%m%d')
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
outputFile = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('outputFile')
generationDate = datetime.date.today().strftime('%Y%m%d')
logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', generationDate)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(), sophisDb.getHost()+"/"+sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()
with open(SQL) as file: sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))

records = ""
if (len(results) > 0 ):
    for internalref in results:
        records += "<tr>"
        records += "<td>" + str(internalref[1]) + "</td>"
        records += "<td>" + str(internalref[2]) + "</td>"
        records += "<td>" + str(internalref[3].strftime('%d-%b-%Y')) + "</td>"
        records += "</tr>\r\n"
    logger.info(records)
    with open(template) as file:
        templateContent = file.read()

    content = templateContent.replace('###Template###', records)
    outputfile = open(outputFile, 'w')
    outputfile.write(content)
    outputfile.close()
    sendEmail(content, generationDate)
else:
   logger.info("No record found!")

