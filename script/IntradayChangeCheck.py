from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import cx_Oracle
import datetime
import smtplib
import time
import math
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage

dateFormat = "YYYYMMDD"

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()


def sendEmail(content, today_str):
    logger.info("Try to send email")
    sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
    receiptent = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Receiptent')
    subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
    body = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Body')
    smptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
    smptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

    subject = subject.replace(dateFormat, today_str)

    subject = subject + " - ALERT"
    msg = EmailMessage()
    msg['From'] = sender
    msg['To'] = receiptent
    msg['Subject'] = subject
    msg.set_content(content)
    msg.add_alternative(content, subtype='html')
    logger.info("================================================================")
    logger.info(msg)
    if (PropertiesUtil.configObject.IsPRD()):
        with smtplib.SMTP(smptHost, smptPort) as server:
            server.send_message(msg)
    else:
        logger.info("No email sending for DEV or UAT")


# with open ('config/sophis.connection.properties') as file: content = file.read()
# print(content)
intradaycount = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('intradaycount')
today_str = datetime.date.today().strftime('%Y%m%d')
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
reportFileName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFileName')
reportFileName = reportFileName.replace('YYYYMMDD', today_str)


today_str = datetime.date.today().strftime('%Y%m%d')
logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', today_str)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(), sophisDb.getHost()+"/"+sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()

textfile=open(intradaycount, 'r')
counts=textfile.read()
logger.info(counts)

print(counts)

with open(SQL) as file:sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql, value=counts)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))

e=len(results)
records = ""

print(len(results))
if (len(results) > 0 ):
    for intradaycheck in results:
        writefile = open(intradaycount, 'w')
        writefile.write(str(e))
        records += "<tr>"
        records += "<td>" + str(intradaycheck[0]) + "</td>"
        records += "<td>" + str(intradaycheck[1]) + "</td>"
        records += "<td>" + str(intradaycheck[2]) + "</td>"
        records += "<td>" + str(intradaycheck[3]) + "</td>"
        records += "<td>" + str(intradaycheck[4]) + "</td>"
        records += "<td>" + str(intradaycheck[5]) + "</td>"
        records += "<td>" + str(intradaycheck[6]) + "</td>"
        records += "<td>" + str(intradaycheck[7]) + "</td>"
        records += "<td>" + str(intradaycheck[8]) + "</td>"
        records += "<td>" + str(intradaycheck[9]) + "</td>"
        records += "<td>" + str(intradaycheck[10]) + "</td>"
        records += "<td>" + str(intradaycheck[11]) + "</td>"
        records += "<td>" + str(intradaycheck[12]) + "</td>"
        records += "</tr>"
        logger.info(records)

    with open(template) as file:
        templateContent = file.read()
        content = templateContent.replace('###Template###', records)
        outputfile = open("output/" + reportFileName, 'w')
        outputfile.write(content)
        outputfile.close()
        sendEmail(content, today_str)
else:
   logger.info("No record found!")
