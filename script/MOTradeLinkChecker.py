## Date         Name                       Detail
##==========================================================================================================================
##20210804      Chris <PERSON>u                 Generate the trade history report for MO
#
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import GTJAEmailSender
import cx_Oracle
import datetime

dateFormat = "YYYYMMDD"
decimal_point = 2

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Header')
businessDate = datetime.date.today().strftime('%Y%m%d')

#Report file
reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFolder')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')

SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')

logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', businessDate)
sophisDb = SophisDBConnection()

with open(SQL) as file: sql = file.read()
logger.info('SQL [' + sql +']')

try:
    # establish a new connection
    with cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance()) as dbConn:
        logger.info(dbConn)
        # create a cursor
        with dbConn.cursor() as cursor:
            # execute the insert statement
            cursor.execute(sql)
            results = cursor.fetchall()
            logger.info('Records = [ %i ]', len(results))
            outputReport = ''
            if (len(results) > 0):
                for internalref in results:
                    logger.info("Test ... [%i] ", internalref[0])
                    outputReport += str(internalref[0]) +'<br>'
                GTJAEmailSender.sendEmail(outputReport, businessDate, dateFormat)
except cx_Oracle.Error as error:
    logger.error('Error occurred:')
    logger.error(error)


