## The WMFP team would like to have market data ready before market open so that the structure products are able to be priced before
## market open.
##
## Date         Name                       Detail
##==========================================================================================================================
## 20210716      Chris Chiu                 First version
##
##
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA.ProcessingDate import ProcessingDate
import cx_Oracle
import datetime

dateFormat = "YYYYMMDD"

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

logger.info("Start processing ... ")

SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
logger.info('SQL = %s', SQL)
with open(SQL) as file: sql = file.read()
logger.info('SQL [' + sql +']')

reportDate = ProcessingDate()
logger.info("Processing date [%s]", str(reportDate.getCurrentDate()))
sophisDb = SophisDBConnection()

try:
    # establish a new connection
    with cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance()) as dbConn:
        logger.info(dbConn)
        # create a cursor
        with dbConn.cursor() as cursor:
            # execute the insert statement
            cursor.execute(sql, today=reportDate.getCurrentDateInString(), lastBusinessDate=reportDate.getLastBusinessDateInString())
            # commit work
            dbConn.commit()

except cx_Oracle.Error as error:
    logger.error('Error occurred:')
    logger.error(error)