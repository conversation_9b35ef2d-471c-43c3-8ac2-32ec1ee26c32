## Date         Name                       Detail
##==========================================================================================================================
##20221130      Chris Chiu                 Generate the payment schedule report for ISD team
#
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import cx_Oracle
import datetime
import csv

dateFormat = "YYYYMMDD"

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
today_str = datetime.date.today().strftime('%Y%m%d')
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Header')
outputFile = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('OutputFileName')
reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('reportFolder')


csvHeaderList = list(header.split(','))
outputFile = outputFile.replace('YYYYMMDD', today_str)
outputFn = open(reportFolder + '/' + outputFile, mode='w', newline='')
csvWriter = csv.writer(outputFn, dialect='excel', quoting=csv.QUOTE_NONNUMERIC)
csvWriter.writerow(csvHeaderList)


logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', today_str)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(), sophisDb.getHost()+"/"+sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()

with open(SQL) as file:sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))

e=len(results)
records = ""

print(len(results))
if (len(results) > 0 ):
    for rec in results:
        logger.info(rec)
        csvWriter.writerow(rec)
else:
   logger.info("No record found!")
