## Date         Name                       Detail
# This is a python report to check the Equity Swap notional threshold for US client reporting.
# Email alert is sent when the targeted threshold rate is reached.
# Example:
# Day 1: >=25% & <50% -> Email
# Day 2: >=25% & <50% -> No Email
# Day 3: >=50% & <75% -> Email
# Day 4: >=25% & <50% -> No Email
# Day 5: >=50% & <75% -> Email
# Day 6: >=75% & <80% -> Email
# Day 7: >=25% & <50% -> No Email
# Day 8: >=50% & <75% -> Email
# Day 9: >=75% & <80% -> Email
#
# ==========================================================================================================================
# 20230214      <PERSON>k                 Initial version
# 20231127      Alvin <PERSON>                 Extend the child class to support the Equity Swap threshold checking.
# 20240225      Alvin Mak                 Add SQLRecord parameter to count if transactions exist.
#

from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from ISDUSReportThreshold import Threshold
import datetime
import configparser

dateFormat = 'YYYYMMDD'

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Header')
businessDate = datetime.date.today()
businessDate = businessDate.strftime('%Y%m%d')

# Report file
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
SQLRecord = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQLRecord')
SQLSum = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQLSum')
SQLDetail = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQLDetail')
outputFilePrefix = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('LogFileSuffix')
defaultFX = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Default_USDCNH_FX')
thresholdAmt = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('ThresholdAmt')
thresholdPercent = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('ThresholdPercent')
thresholdPercentList = list(thresholdPercent.split(","))
nextThresholdConfigPath = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('NextThresholdConfig')

# Read the Next Threshold file
config = configparser.ConfigParser()
config.read(nextThresholdConfigPath)

logger.info('SQLRecord = %s', SQLRecord)
logger.info('SQLSum = %s', SQLSum)
logger.info('SQLDetail = %s', SQLDetail)


class EquitySwapThreshold(Threshold):
    def __init__(self, template, SQLRecord, SQLSum, SQLDetail, outputFilePrefix, defaultFX, thresholdAmt, thresholdPercent,
                 thresholdPercentList, nextThresholdConfigPath, config):
        logger.info('EquitySwapThreshold Child Class: __init__')
        self.template = template
        self.SQLRecord = SQLRecord
        self.SQLSum = SQLSum
        self.SQLDetail = SQLDetail
        self.outputFilePrefix = outputFilePrefix
        self.defaultFX = defaultFX
        self.thresholdAmt = thresholdAmt
        self.thresholdPercent = thresholdPercent
        self.thresholdPercentList = thresholdPercentList
        self.nextThresholdConfigPath = nextThresholdConfigPath
        self.config = config
        super().__init__(template, SQLRecord, SQLSum, SQLDetail, outputFilePrefix, defaultFX, thresholdAmt, thresholdPercent,
                         thresholdPercentList, nextThresholdConfigPath, config)

    def main(self):
        logger.info('EquitySwapThreshold Child Class: main()')
        super().main()


# Instantiate the Equity Swap child class
equity_swap_threshold = EquitySwapThreshold(template, SQLRecord, SQLSum, SQLDetail, outputFilePrefix, defaultFX, thresholdAmt,
                                            thresholdPercent, thresholdPercentList, nextThresholdConfigPath, config)
equity_swap_threshold.main()