'''
Date        Name            Detail
========================================================================================================================
20250810    Alvin Mak       Initial version of HKTR Valuation report to handle in-scope allotments.
'''

import logging
import operator

from .xml_key_util import check_nested_key
from .default_product import default_product
from datetime import datetime


class HKTR_valuations_allotments(default_product):

    def __init__(self):
        # self.__allotment__= ['Equity Swap', 'Index Swap', 'Futures Swap', 'TRS CB']
        # self.__allotment__ = ['Accumulators', 'Decumulators', 'Fixed Cpn Swap']
        # self.__allotment__ = ['OTC Options', 'Bond Option', 'Digital Options']
        # self.__allotment__ = ['Cross Currency Swap', 'Interest Rate Swap', 'CDS']
        # self.__allotment__ = ['TRS Bonds', 'TRS Bonds - Basket']
        # self.__allotment__ = ['FX Forward', 'NDF', 'FX Spot']
        self.__allotment__ = ['Equity Swap', 'Index Swap', 'Futures Swap', 'TRS CB',
                              'Accumulators', 'Decumulators', 'Fixed Cpn Swap',
                              'OTC Options', 'Bond Option', 'Digital Options',
                              'Cross Currency Swap', 'Interest Rate Swap', 'CDS',
                              'TRS Bonds', 'TRS Bonds - Basket',
                              'FX Forward', 'NDF', 'FX Spot']
        self.__header__ = list()
        self.__header__.append('VALUATION_DATE')
        self.__header__.append('EXT_TRADE_ID')
        self.__header__.append('FUND')
        self.__header__.append('CPY')
        self.__header__.append('TRADE_TYPE')
        self.__header__.append('PAY_RECEIVE')
        self.__header__.append('NOTIONAL1')
        self.__header__.append('CCY1')
        self.__header__.append('NOTIONAL2')
        self.__header__.append('CCY2')
        self.__header__.append('UNDERLYING')
        self.__header__.append('TRADE_DATE')
        self.__header__.append('START_DATE')
        self.__header__.append('MATURITY_DATE')
        self.__header__.append('MTM')
        self.__header__.append('MTM_CCY')
        self.__header__.append('NIA')
        self.__header__.append('NIA_CCY')
        self.__header__.append('NNIA')
        self.__header__.append('NNIA_CCY')
        self.__header__.append('UTI')
        self.__header__.append('FIXED_RATE_LEG_1')
        self.__header__.append('FIXED_RATE_LEG_2')
        self.__header__.append('FIXED_RATE_DAY_COUNT')
        self.__header__.append('FIXED_RATE_PAYMENT_FREQ')
        self.__header__.append('FLOATING_RATE_PAYMENT_FREQ')
        self.__header__.append('FLOATING_RATE_RESET_FREQ')
        self.__header__.append('FLOATING_RATE_LEG_1')
        self.__header__.append('FLOATING_RATE_LEG_2')
        self.__header__.append('EXCHANGE_RATE_1')
        self.__header__.append('FORWARD_EXCHANGE_RATE')
        self.__header__.append('EXCHANGE_RATE_BASIS')
        self.__header__.append('EXECUTION_TIMESTAMP')
        self.__header__.append('CONFIRMATION_TIMESTAMP')
        self.__header__.append('MASTER_AGREEMENT_VERSION')
        self.__header__.append('IS_CLEARED')
        self.__header__.append('USER_DEFINED_FIELD_1')
        self.__header__.append('USER_DEFINED_FIELD_2')
        self.__header__.append('CPY_TRADE_ID')
        self.__header__.append('FUND_LEI')
        self.__header__.append('CPY_LEI')
        self.__header__.append('TIMESTAMP')
        self.__header__.append('DELTA')

    def generate_positions(self, positions):
        csvRecords = list()
        for position in positions:
            if position['instrumentDetail']['allotment'] in ['Accumulators', 'Decumulators', 'Fixed Cpn Swap']:
                trade_var = 'TradeDetails'
            else:
                trade_var = 'Trades'
            if position['instrumentDetail']['allotment'] in self.__allotment__:
                for trade in position[trade_var]:
                    if check_nested_key(position, 'endDate', 'date'):
                        expiry_date = datetime.strptime(position['endDate']['date'], '%Y-%m-%d')
                    else:
                        expiry_date = datetime.strptime(trade['paymentDate']['date'], '%Y-%m-%d')
                    today = datetime.now()
                    quantity = (trade['quantity'])
                    backoffice_status = trade['backofficeStatus']
                    # if today < expiry_date and quantity != 0 and backoffice_status != 'MO Cancelled':
                    if quantity != 0 and backoffice_status != 'MO Cancelled':
                        csvRecord = list()
                        try:
                            csvRecord.append(position['valuationDate']['date'])
                            csvRecord.append(position['instrumentDetail']['referenceData']['reference'])
                            csvRecord.append(trade['entity']['name'])
                            csvRecord.append(trade['counterparty']['name'])
                            csvRecord.append(position['instrumentDetail']['allotment'])
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            if position['instrumentDetail']['allotment'] in ['Accumulators', 'Decumulators',
                                                                             'OTC Options', 'Bond Option',
                                                                             'Digital Options', 'FX Forward', 'NDF',
                                                                             'FX Spot']:
                                csvRecord.append(trade['markToMarket']['mtmUSD']['amount'])
                                csvRecord.append(trade['markToMarket']['mtmUSD']['currency']['ISO'])
                            else:
                                csvRecord.append(position['markToMarket']['mtmUSD']['amount'])
                                csvRecord.append(position['markToMarket']['mtmUSD']['currency']['ISO'])
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append(trade['UTI'])
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append("")
                            csvRecord.append(trade['counterPartyLEI'])
                            csvRecord.append(trade['entityLEI'])
                            csvRecord.append(trade['lastModificationDate']['date'])
                            if check_nested_key(position, 'delta'):
                                csvRecord.append(position['delta'])
                            else:
                                csvRecord.append("")

                            csvRecords.append(csvRecord)
                        except Exception as err:
                            csvRecord.append('<<<' + str(err) + '>>>')
                    else:
                        logging.warning(position['instrumentDetail']['referenceData']['reference'] + ' has expired!')

        csvRecords = sorted(csvRecords, key=operator.itemgetter(4, 1))
        return csvRecords
