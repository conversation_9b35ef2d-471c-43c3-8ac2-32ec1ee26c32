##
##  Name        Date                Description
##==========================================================================================================
##  <PERSON>  23-March-2021       Use Side as a order key to handle the Future Spread order
##  Chris <PERSON>  25-April-2022       Mark the trade to processed if the quantity and price are mis-match.
##
from GTJA.SophisDBConnection import SophisDBConnection
from Sophis.JetTradeRepository import JetTradeRepository
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import GTJAEmailSender
import cx_Oracle
import datetime
import os.path
import time

dateFormat = "YYYYMMDD"
SLEEP_TIME = 30

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
businessDate = datetime.date.today().strftime('%Y%m%d')

# Report file
jetTradeFile = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('JetTradeFile')
jetTradeFile = jetTradeFile.replace('YYYYMMDD', businessDate)

while (os.path.isfile(jetTradeFile) == False):
    logger.warning("file, %s, not exist, sleep %d sec ", jetTradeFile, SLEEP_TIME)
    time.sleep(SLEEP_TIME)
time.sleep(SLEEP_TIME)
logger.info('Start processing %s ...', jetTradeFile)

jetTradeRepository = JetTradeRepository(jetTradeFile)

logger.info('Jet Trade file')
logger.debug(jetTradeRepository)

SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
DUPLICATE_SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('DUPLICATE_TRADE_SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')

logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', businessDate)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()
with open(SQL) as file: sql = file.read()
logger.info("SQL query = %s", sql)
# cursor.execute(sql)
cursor.execute(sql, today=businessDate)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))
unmatchedTrades = dict()
notInJET = list()
if (len(results) > 0):
    for record in results:
        # logger.info('%s ', record)
        key = record[0]
        price = record[1]
        quantity = record[2]
        status = record[3]
        sophisTradeId = record[4]
        if quantity > 0:
            side = 'B'
        else:
            side = 'S'

        tradeKey = key + '@' + side
        logger.info('Trade Key [%s] %s', tradeKey, sophisTradeId)
        sophisPrice = float("{:.6f}".format(round(price, 7)))
        r = [key, sophisTradeId, sophisPrice, quantity, False]
        if (tradeKey in jetTradeRepository.getJetTradeRepository()):
            logger.info('Handling %s', sophisTradeId)
            # logger.info('%s exist ', record)
            jetTrade = jetTradeRepository.getJetTradeRepository()[tradeKey]
            # sophisPrice = price
            # jetTradePrice = jetTrade.getAveragePrice()
            jetTradePrice = float("{:.6f}".format(round(jetTrade.getAveragePrice(), 7)))

            if (tradeKey in unmatchedTrades):
                container = unmatchedTrades[tradeKey]
            else:
                container = list()
            if (jetTrade.getSophisQuantity() != quantity):
                logger.info('%d %d ', jetTrade.getSophisQuantity(), quantity)
                logger.info('%s quantity not match %s ', record, jetTrade)
                container.append(r)
                unmatchedTrades[tradeKey] = container
                jetTrade.matched()
            elif (jetTradePrice != sophisPrice):
                container.append(r)
                logger.info('%s %s ', jetTrade.getAveragePrice(), price)
                logger.info('%s price not match %s ', record, jetTrade)
                unmatchedTrades[tradeKey] = container
                jetTrade.matched()
            else:
                jetTrade.matched()
        else:
            logger.warn('Not in JET ... %s', sophisTradeId)
            notInJET.append(r)
            logger.info('%s not exist ', record)

with open(DUPLICATE_SQL) as file: sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql, today=businessDate)
results = cursor.fetchall()
duplicatedTrades = list()
if (len(results) > 0):
    for record in results:
        logger.info(record)
        duplicatedTrades.append(record)

missingTrades = False
for re in jetTradeRepository.getJetTradeRepository():
    t = jetTradeRepository.getJetTradeRepository()[re]
    if not t.match:
        missingTrades = True
        break

logger.info(
    '==========================================================================================================')
if (len(unmatchedTrades) > 0 or len(notInJET) > 0 or len(duplicatedTrades) > 0 or missingTrades):
    outputMessage = ''
    for key in unmatchedTrades:
        logger.info('%s', key)
        container = unmatchedTrades[key]
        for rec in container:
            outputMessage += '<tr>'
            outputMessage += '<td>' + key + '</td>'
            outputMessage += '<td>' + jetTradeRepository.getJetTradeRepository()[key].side + '</td>'
            outputMessage += '<td>' + "{:.6f}".format(
                jetTradeRepository.getJetTradeRepository()[key].getAveragePrice()) + '</td>'
            outputMessage += '<td>' + "{:.0f}".format(
                jetTradeRepository.getJetTradeRepository()[key].quantity) + '</td>'
            outputMessage += '<td>' + str(rec[1]) + '</td>'
            outputMessage += '<td>' + "{:.6f}".format(rec[2]) + '</td>'
            outputMessage += '<td>' + str(rec[3]) + '</td>'
            outputMessage += '</tr>'

    for jet in jetTradeRepository.getJetTradeRepository().values():
        if jet.match == False:
            logger.info('JET unmatched [%s]', jet)
            outputMessage += '<tr>'
            outputMessage += '<td>' + jet.orderId + '</td>'
            outputMessage += '<td>' + jet.side + '</td>'
            outputMessage += '<td>' + "{:.6f}".format(jet.getAveragePrice()) + '</td>'
            outputMessage += '<td>' + "{:.0f}".format(jet.quantity) + '</td>'
            outputMessage += '<td></td>'
            outputMessage += '<td></td>'
            outputMessage += '<td></td>'
            outputMessage += '</tr>'

    for rec in notInJET:
        logger.info('[%s, %s, %s, %s, %s]', rec[0], rec[1], rec[2], rec[3], rec[4])
        outputMessage += '<tr>'
        outputMessage += '<td>' + rec[0] + '</td>'
        outputMessage += '<td></td>'
        outputMessage += '<td></td>'
        outputMessage += '<td></td>'
        outputMessage += '<td>' + str(rec[1]) + '</td>'
        outputMessage += '<td>' + "{:.6f}".format(rec[2]) + '</td>'
        outputMessage += '<td>' + str(rec[3]) + '</td>'
        outputMessage += '</tr>'

    duplicateOutputMessage = ''
    if (len(duplicatedTrades) > 0):
        duplicateOutputMessage = '<h3>Duplicated Trades</h3>'
        duplicateOutputMessage += '<tr bgcolor="#9acd32"><th>Sophis Trade Id</th><th>JET Order</th><th>JET Trade</th><th>Sophis Price</th><th>Sophis Quantity</th></tr>'
        for duplicate in duplicatedTrades:
            duplicateOutputMessage += '<tr>'
            duplicateOutputMessage += '<td>' + str(duplicate[0]) + '</td>'
            duplicateOutputMessage += '<td>' + duplicate[1] + '</td>'
            duplicateOutputMessage += '<td>' + duplicate[2] + '</td>'
            duplicateOutputMessage += '<td>' + str(duplicate[3]) + '</td>'
            duplicateOutputMessage += '<td>' + str(duplicate[4]) + '</td>'
            duplicateOutputMessage += '</tr>'

    with open(template) as file:
        templateContent = file.read()
        content = templateContent.replace('###Template###', outputMessage)
        content = content.replace('###DuplicateTemplate###', duplicateOutputMessage)
        outputfile = open('output/WMJETTradeRecon.html', 'w')
        outputfile.write(content)
        outputfile.close()
        GTJAEmailSender.sendEmail(content, businessDate, dateFormat)
else:
    logger.info('No unmatch record')
