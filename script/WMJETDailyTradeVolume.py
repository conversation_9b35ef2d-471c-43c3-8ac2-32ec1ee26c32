## Date         Name                       Detail
# This is a python report to extract the daily trading volume and send by email.
#
#==========================================================================================================================
# 20210923      Chris Chiu                 First version
#
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJAFormatter import GTJAFormatter
from GTJA.GTJALogging import GTJALogging
from GTJA.DateDeltaHandler import DateDel<PERSON><PERSON>andler
from GTJA import PropertiesUtil
import cx_Oracle
import datetime
from GTJA import GTJAEmailSender

dateFormat = "YYYYMMDD"
decimal_point = 2

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Header')
businessDate = datetime.date.today()

#Report file
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
outputFilePrefix = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('LogFileSuffix')
dayDelta = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('dayDelta')

logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', businessDate)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance())


dateDeltaHandler = DateDeltaHandler(businessDate, dayDelta)
fromDate = dateDeltaHandler.getDeltaDate().strftime('%Y%m%d')
toDate = businessDate.strftime('%Y%m%d')
businessDate = businessDate.strftime('%Y%m%d')
logger.info("fromDate = %s ", str(fromDate))
logger.info("TDate = %s ", str(toDate))
logger.info(dbConn)
cursor = dbConn.cursor()
with open(SQL) as file: sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql, fromDate=fromDate, toDate=toDate)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))

outputReport = ''
totalVolume = 0
if (len(results) > 0):
    for internalref in results:
        tradeDate = internalref[0]
        allotment = internalref[1]
        tradingVolume = internalref[2]
        totalVolume += tradingVolume
        outputReport += '<tr>'
        outputReport += '<td>' + tradeDate.strftime('%d-%b-%Y') + '</td>'
        outputReport += '<td>' + allotment + '</td>'
        outputReport += '<td align=\'right\'>' + GTJAFormatter.formatAmount(tradingVolume, 2) + '</td>'
        outputReport += '</tr>'
else:
    outputReport +='<tr> <td> No Record </td></tr>'

if ( totalVolume > 0 ):
    outputReport += '<tr><td></td><td>Total Volume</td><td align=\'right\'>' + GTJAFormatter.formatAmount(totalVolume, 2) + '</td></tr>'

with open(template) as file:
    templateContent = file.read()
    content = templateContent.replace('###Template###', outputReport)
    outputfile = open('output/'+outputFilePrefix+'.html', 'w')
    logger.info(content)
    outputfile.write(content)
    outputfile.close()
    GTJAEmailSender.sendEmail(content, businessDate, dateFormat)


