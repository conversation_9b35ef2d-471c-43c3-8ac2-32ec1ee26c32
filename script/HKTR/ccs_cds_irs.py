'''
Date        Name            Detail
========================================================================================================================
20250128    Alvin Mak       Initial version to handle HKTR trade extractions.
                            Cross Currecny Swap, Credit Default Swap Interest Rate Swap
'''

import logging
import operator
from .default_product import default_product
from .xml_key_util import check_nested_key
from datetime import datetime


class ccs_cds_irs(default_product):

    def __init__(self):
        self.__allotment__ = ['Cross Currency Swap', 'Interest Rate Swap', 'CDS']
        self.__header__ = list()
        self.__header__.append('Instrument Reference')
        self.__header__.append('Portfolio')
        self.__header__.append('Trade ID')
        self.__header__.append('UTI')
        self.__header__.append('Allotment')
        self.__header__.append('Action')
        self.__header__.append('Business Event')
        self.__header__.append('Trade Date')
        self.__header__.append('Start Date')
        self.__header__.append('Maturity Date')
        self.__header__.append('Execution DateTime')
        self.__header__.append('Initial Execution DateTime')
        self.__header__.append('Reporting Party LEI')
        self.__header__.append('NonReporting Party LEI')
        self.__header__.append('Depository LEI')
        self.__header__.append('Depository Reference')
        self.__header__.append('Depository Name')
        self.__header__.append('Counterparty Reference')
        self.__header__.append('Counterparty Name')
        self.__header__.append('Quantity')
        self.__header__.append('Payment Date')
        self.__header__.append('Payment Currency')
        self.__header__.append('Receiving Leg Type')
        self.__header__.append('Receiving Leg Rate')
        self.__header__.append('Receiving Leg IRIndex')
        self.__header__.append('Receiving Leg Reset Frequency')
        self.__header__.append('Receiving Leg Coupon Basis')
        self.__header__.append('Receiving Leg Currency')
        self.__header__.append('Receiving Leg Cap Frequency')
        self.__header__.append('Receiving Leg Spread')
        self.__header__.append('Receiving Leg Initial Exchange')
        self.__header__.append('Paying Leg Type')
        self.__header__.append('Paying Leg Rate')
        self.__header__.append('Paying Leg IRIndex')
        self.__header__.append('Paying Leg Reset Frequency')
        self.__header__.append('Paying Leg Coupon Basis')
        self.__header__.append('Paying Leg Currency')
        self.__header__.append('Paying Leg Cap Frequency')
        self.__header__.append('Paying Leg Spread')
        self.__header__.append('Underlying Instrument Currency')
        self.__header__.append('Price Type')
        self.__header__.append('Quantity')
        self.__header__.append('Net Amount')
        self.__header__.append('Net Amount Currency')
        self.__header__.append('Notional1')
        self.__header__.append('Notional1 Currency')
        self.__header__.append('Notional2')
        self.__header__.append('Notional2 Currency')
        self.__header__.append('Underlying Reference')
        self.__header__.append('Underlying Name')
        self.__header__.append('Underlying Exchange')
        self.__header__.append('ISIN')
        self.__header__.append('Clearing Time')
        self.__header__.append('CDS Reference')

    def generate_positions(self, positions, reportDate):
        CLEARING_TIME = "23:59:59"
        csv_records = list()
        for position in positions:
            if position['instrumentDetail']['allotment'] in self.__allotment__:
                for trade in position['Trades']:
                    expiry_date = datetime.strptime(position['endDate']['date'], '%Y-%m-%d')
                    report_date = datetime.strptime(reportDate, "%Y-%m-%d")
                    #if expiry_date > report_date and position['quantity'] != 0:
                    #if expiry_date > report_date:
                    if 1==1:
                        csv_record = list()
                        try:
                            lastModificationDate = trade['lastModificationDate']['date']
                            tradeResend = trade['UTI']['tradeResend']
                            if lastModificationDate <= reportDate or tradeResend:
                                previousUTI = trade['UTI']['previousUTI']
                                UTI = position['UTI']
                                if len(previousUTI) > 0:
                                    UTI = previousUTI
                                csv_record.append(position['instrumentDetail']['referenceData']['reference'])
                                csv_record.append(position['portfolio'])
                                csv_record.append(trade['ticketId'])
                                csv_record.append(position['UTI'])
                                csv_record.append(position['instrumentDetail']['allotment'])
                                csv_record.append(trade['actionType'])
                                csv_record.append(trade['businessEvent']['name'])
                                csv_record.append(position['tradeDate']['date'])
                                csv_record.append(position['startDate']['date'])
                                csv_record.append(position['endDate']['date'])
                                csv_record.append(trade['transactionDateTime']['dateTime'])
                                csv_record.append(position['initialTransactionDateTime']['dateTime'])
                                csv_record.append(trade['entityLEI'])
                                csv_record.append(trade['counterPartyLEI'])
                                csv_record.append(trade['depositoryLEI'])
                                csv_record.append(trade['depository']['reference'])
                                csv_record.append(trade['depository']['name'])
                                csv_record.append(trade['counterparty']['reference'])
                                csv_record.append(trade['counterparty']['name'])
                                csv_record.append(trade['quantity'])
                                csv_record.append(trade['paymentDate']['date'])
                                csv_record.append(position['settlementCurrency']['ISO'])
                                csv_record.append(position['receivingLegInfo']['type'])
                                csv_record.append(position['receivingLegInfo']['value'])
                                csv_record.append(position['receivingLegInfo']['irIndex']['reference'])
                                csv_record.append(position['receivingLegInfo']['frequency']['name'])
                                csv_record.append(position['receivingLegInfo']['couponBasis']['name'])
                                csv_record.append(position['receivingLegInfo']['currency']['ISO'])
                                csv_record.append(position['receivingLegInfo']['capFrequency']['name'])
                                csv_record.append(position['receivingLegInfo']['spread'])
                                csv_record.append(position['receivingLegInfo']['initialForexSpot'])
                                csv_record.append(position['payingLegInfo']['type'])
                                csv_record.append(position['payingLegInfo']['value'])
                                csv_record.append(position['payingLegInfo']['irIndex']['reference'])
                                csv_record.append(position['payingLegInfo']['frequency']['name'])
                                csv_record.append(position['payingLegInfo']['couponBasis']['name'])
                                csv_record.append(position['payingLegInfo']['currency']['ISO'])
                                csv_record.append(position['payingLegInfo']['capFrequency']['name'])
                                csv_record.append(position['payingLegInfo']['spread'])
                                csv_record.append(position['underlying']['productCurrency']['ISO'])
                                csv_record.append(trade['priceType']['name'])
                                csv_record.append(trade['quantity'])
                                csv_record.append(trade['notional']['currency']['ISO'])
                                csv_record.append(round(trade['notional']['amount'], 2))
                                csv_record.append(round(position['notional1']['amount'], 2))
                                csv_record.append(position['notional1']['currency']['ISO'])
                                csv_record.append(round(position['notional2']['amount'], 2))
                                csv_record.append(position['notional2']['currency']['ISO'])
                                csv_record.append(position['underlying']['referenceData']['reference'])
                                csv_record.append(position['underlying']['referenceData']['name'])
                                csv_record.append(position['underlying']['exchange'])
                                if check_nested_key(position, 'underlying', 'references', 'ISIN'):
                                    csv_record.append(position['underlying']['references']['ISIN'])
                                else:
                                    csv_record.append('')
                                csv_record.append(CLEARING_TIME)
                                if check_nested_key(position, 'underlying', 'references', 'CDSReference'):
                                    csv_record.append(position['underlying']['references']['CDSReference'])
                                else:
                                    csv_record.append('')

                                csv_records.append(csv_record)
                        except Exception as err:
                            csv_record.append('<<<' + str(err) + '>>>')
                    else:
                        logging.warning(position['instrumentDetail']['referenceData']['reference'] + ' has expired!')

        csv_records = sorted(csv_records, key=operator.itemgetter(0, 1))
        return csv_records

