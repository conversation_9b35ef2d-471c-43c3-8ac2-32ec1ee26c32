'''
Date        Name            Detail
========================================================================================================================
20250128    Alvin Mak       Add Accumulators and Decumulators parser. This parser supports trade level extraction.
'''
import logging
import operator
from .default_product import default_product
from .common_util import check_nested_key
from datetime import datetime


class accu_decu(default_product):

    def __init__(self):
        self.__allotment__ = ['Accumulators', 'Decumulators']
        self.__header__ = list()
        self.__header__.append('Instrument Reference')
        self.__header__.append('Portfolio')
        self.__header__.append('Trade ID')
        self.__header__.append('UTI')
        self.__header__.append('Allotment')
        self.__header__.append('Action')
        self.__header__.append('Business Event')
        self.__header__.append('Trade Date')
        self.__header__.append('Start Date')
        self.__header__.append('Maturity Date')
        self.__header__.append('Execution DateTime')
        self.__header__.append('Initial Execution DateTime')
        self.__header__.append('Reporting Party LEI')
        self.__header__.append('NonReporting Party LEI')
        self.__header__.append('Counterparty Reference')
        self.__header__.append('Counterparty Name')
        self.__header__.append('Quantity')
        self.__header__.append('Nominal')
        self.__header__.append('Final Notional')
        self.__header__.append('Net Amount')
        self.__header__.append('Counterparty Fees')
        self.__header__.append('Payment Date')
        self.__header__.append('Payment Currency')
        self.__header__.append('Price Type')
        self.__header__.append('Fixing Date')
        self.__header__.append('Strike Price')
        self.__header__.append('Strike Currency')
        self.__header__.append('Knock Out Price')
        self.__header__.append('Leverage')
        self.__header__.append('Guaranteed')
        self.__header__.append('GTJASpotPx')
        self.__header__.append('GTJAStrikePercent')
        self.__header__.append('GTJAKOPercent')
        self.__header__.append('Period Start Date Schedule')
        self.__header__.append('Period End Date Schedule')
        self.__header__.append('Period Notional Currency')
        self.__header__.append('Period Notional Amount')
        self.__header__.append('Underlying Reference')
        self.__header__.append('Underlying Name')
        self.__header__.append('Underlying Exchange')
        self.__header__.append('Underlying Type')
        self.__header__.append('Underlying ISIN')
        self.__header__.append('Underlying Unit')

    def generate_positions(self, positions, reportDate):
        csv_records = list()
        for position in positions:
            if position['instrumentDetail']['allotment'] in self.__allotment__:
                for trade in position['Trades']:
                    expiry_date = datetime.strptime(position['endDate']['date'], '%Y-%m-%d')
                    report_date = datetime.strptime(reportDate, "%Y-%m-%d")
                    if expiry_date > report_date and trade['quantity'] != 0:
                        csv_record = list()
                        try:
                            lastModificationDate = trade['lastModificationDate']['date']
                            tradeResend = trade['UTI']['tradeResend']
                            if lastModificationDate <= reportDate or tradeResend:
                                previousUTI = trade['UTI']['previousUTI']
                                UTI = position['UTI']
                                if len(previousUTI) > 0:
                                    UTI = previousUTI
                                csv_record.append(position['instrumentDetail']['referenceData']['reference'])
                                csv_record.append(position['portfolio'])
                                csv_record.append(trade['ticketId'])
                                csv_record.append(position['UTI'])
                                csv_record.append(position['instrumentDetail']['allotment'])
                                csv_record.append(trade['actionType'])
                                csv_record.append(trade['businessEvent']['name'])
                                csv_record.append(position['tradeDate']['date'])
                                csv_record.append(position['IssueDate']['date'])
                                csv_record.append(position['endDate']['date'])
                                csv_record.append(trade['transactionDateTime']['dateTime'])
                                csv_record.append(position['initialTransactionDateTime']['dateTime'])
                                csv_record.append(trade['entityLEI'])
                                csv_record.append(trade['counterPartyLEI'])
                                csv_record.append(trade['counterparty']['reference'])
                                csv_record.append(trade['counterparty']['name'])
                                csv_record.append(trade['quantity'])
                                csv_record.append(trade['nominal'])
                                csv_record.append(position['notional1']['amount'])
                                csv_record.append(round(trade['notional']['amount'], 2))
                                csv_record.append(trade['counterpartyFees']['amount'])
                                csv_record.append(trade['paymentDate']['date'])
                                csv_record.append(position['SettlementCurrency']['ISO'])
                                csv_record.append(trade['priceType']['name'])
                                fixingDate = position['fixingDate']['sophisDate']
                                if fixingDate != 0:
                                    csv_record.append(position['fixingDate']['date'])
                                else:
                                    csv_record.append('')
                                clauseName = [d['ClauseName'] for d in position['PayOffs']]
                                i = 0
                                cliquet_strike = 0
                                for clauseItem1 in clauseName:
                                    if clauseItem1 == 'M_eCliquet':
                                        cliquet_strike = position['PayOffs'][i]['Strike']
                                        break
                                    else:
                                        i += 1
                                csv_record.append(cliquet_strike)
                                csv_record.append(position['instrumentDetail']['productCurrency']['ISO'])
                                j = 0
                                exitUp_strike = 0
                                for clauseItem2 in clauseName:
                                    if clauseItem2 == 'M_eExitUp':
                                        exitUp_strike = position['PayOffs'][j]['Strike']
                                        break
                                    else:
                                        j += 1
                                csv_record.append(exitUp_strike)
                                k = 0
                                leverage = 0
                                for clauseItem3 in clauseName:
                                    if clauseItem3 == 'M_eLeverage':
                                        leverage = position['PayOffs'][k]['Value']
                                        break
                                    else:
                                        k += 1
                                csv_record.append(leverage)
                                l = 0
                                guaranteed = 0
                                for clauseItem4 in clauseName:
                                    if clauseItem4 == 'M_eGuaranteed':
                                        guaranteed = position['PayOffs'][l]['EndFixingSize']
                                        break
                                    else:
                                        l += 1
                                csv_record.append(guaranteed)
                                csv_record.append(position['instrumentDetail']['references']['GTJASpotPx'])
                                csv_record.append(position['instrumentDetail']['references']['GTJAStrikePercent'])
                                csv_record.append(position['instrumentDetail']['references']['GTJAKOPercent'])
                                csv_record.append(trade['startDateSchedule'])
                                csv_record.append(trade['endDateSchedule'])
                                csv_record.append(trade['notionalCurrency'])
                                csv_record.append(trade['notionalAmount'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingRef'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingName'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingExchange'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingType'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingISIN'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingUnit'])

                                csv_records.append(csv_record)
                        except Exception as err:
                            print(err, trade)
                            csv_record.append('<<<' + str(err) + '>>>')
                    else:
                        logging.warning(position['instrumentDetail']['referenceData']['reference'] + ' has expired!')

        csv_records = sorted(csv_records, key=operator.itemgetter(0, 1))
        return csv_records
