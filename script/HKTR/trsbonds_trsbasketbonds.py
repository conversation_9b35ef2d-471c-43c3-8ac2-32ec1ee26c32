'''
Date        Name            Detail
========================================================================================================================
20250128    Alvin Mak       Initial version to handle HKTR trade extractions.
                            TRS Bonds, TRS Basket Bonds
'''

import logging
import operator
from .default_product import default_product
from .xml_key_util import check_nested_key
from datetime import datetime


class trsbonds_trsbasketbonds(default_product):

    def __init__(self):
        self.__allotment__ = ['TRS Bonds', 'TRS Bonds - Basket']
        self.__header__ = list()
        self.__header__.append('Instrument Reference')
        self.__header__.append('Portfolio')
        self.__header__.append('Trade ID')
        self.__header__.append('UTI')
        self.__header__.append('Allotment')
        self.__header__.append('Action')
        self.__header__.append('Business Event')
        self.__header__.append('Trade Date')
        self.__header__.append('Issue Date')
        self.__header__.append('Maturity Date')
        self.__header__.append('Execution DateTime')
        self.__header__.append('Initial Execution DateTime')
        self.__header__.append('Reporting Party LEI')
        self.__header__.append('NonReporting Party LEI')
        self.__header__.append('Counterparty Reference')
        self.__header__.append('Counterparty Name')
        self.__header__.append('Quantity')
        self.__header__.append('Currency')
        self.__header__.append('Net Amount')
        self.__header__.append('Basis/Nominal')
        self.__header__.append('Payment Date')
        self.__header__.append('Price Type')
        self.__header__.append('IM Factor')
        self.__header__.append('IM Amount')
        self.__header__.append('Underlying Instrument Reference')
        self.__header__.append('Underlying Instrument Name')
        self.__header__.append('Exchange')
        self.__header__.append('IA Reference')
        self.__header__.append('IA Name')
        self.__header__.append('IA Exchange')
        self.__header__.append('IA Currency')
        self.__header__.append('IA Amount')
        self.__header__.append('FA Reference')
        self.__header__.append('FA Name')
        self.__header__.append('FA Exchange')
        self.__header__.append('FA Currency')
        self.__header__.append('FA Amount')
        self.__header__.append('FA IR Index')
        self.__header__.append('FA Frequency')
        self.__header__.append('FA Coupon Basis')
        self.__header__.append('FA Spread')
        self.__header__.append('FA Cap Frequency')
        self.__header__.append('FA Redemption End Dates')
        self.__header__.append('FA Fixed Rate')
        self.__header__.append('Bond Reference')
        self.__header__.append('Bond Name')
        self.__header__.append('Bond Currency')
        self.__header__.append('Bond Amount')
        self.__header__.append('Bond Exchange')
        self.__header__.append('Bond Unit')
        self.__header__.append('Bond Identifier')
        self.__header__.append('Bond Identifier Type')

    def generate_positions(self, positions, reportDate):
        csv_records = list()
        for position in positions:
            if position['instrumentDetail']['allotment'] in self.__allotment__:
                for trade in position['Trades']:
                    expiry_date = datetime.strptime(position['endDate']['date'], '%Y-%m-%d')
                    report_date = datetime.strptime(reportDate, "%Y-%m-%d")
                    if expiry_date > report_date and trade['quantity'] != 0:
                        csv_record = list()
                        try:
                            lastModificationDate = trade['lastModificationDate']['date']
                            tradeResend = trade['UTI']['tradeResend']
                            if lastModificationDate <= reportDate or tradeResend:
                                previousUTI = trade['UTI']['previousUTI']
                                UTI = position['UTI']
                                if len(previousUTI) > 0:
                                    UTI = previousUTI
                                csv_record.append(position['instrumentDetail']['referenceData']['reference'])
                                csv_record.append(position['portfolio'])
                                csv_record.append(trade['ticketId'])
                                csv_record.append(UTI)
                                csv_record.append(position['instrumentDetail']['allotment'])
                                csv_record.append(trade['actionType'])
                                csv_record.append(trade['businessEvent']['name'])
                                csv_record.append(position['tradeDate']['date'])
                                csv_record.append(position['startDate']['date'])
                                csv_record.append(position['endDate']['date'])
                                csv_record.append(trade['transactionDateTime']['dateTime'])
                                csv_record.append(position['initialTransactionDateTime']['dateTime'])
                                csv_record.append(trade['entityLEI'])
                                csv_record.append(trade['counterPartyLEI'])
                                csv_record.append(trade['counterparty']['reference'])
                                csv_record.append(trade['counterparty']['name'])
                                csv_record.append(trade['quantity'])
                                csv_record.append(position['instrumentDetail']['productCurrency']['ISO'])
                                csv_record.append(round(trade['notional']['amount'], 2))
                                csv_record.append(position['basis']['amount'])
                                csv_record.append(trade['paymentDate']['date'])
                                csv_record.append(trade['priceType']['name'])
                                csv_record.append(trade['imFactor'])
                                csv_record.append(position['initialAmount']['amount'])
                                csv_record.append(position['underlying']['referenceData']['reference'])
                                csv_record.append(position['underlying']['referenceData']['name'])
                                csv_record.append(position['underlying']['exchange'])

                                for imComp in position['fundings']['initial_margin']['components']:
                                    csv_record.append(imComp['instrument']['referenceData']['reference'])
                                    csv_record.append(imComp['instrument']['referenceData']['name'])
                                    csv_record.append(imComp['instrument']['exchange'])
                                    csv_record.append(imComp['instrument']['productCurrency']['ISO'])
                                    csv_record.append(imComp['amount'])
                                for financingComp in position['fundings']['financing']['components']:
                                    csv_record.append(financingComp['instrument']['referenceData']['reference'])
                                    csv_record.append(financingComp['instrument']['referenceData']['name'])
                                    csv_record.append(financingComp['instrument']['exchange'])
                                    csv_record.append(financingComp['instrument']['productCurrency']['ISO'])
                                    csv_record.append(financingComp['amount'])
                                for financingDetails in position['fundings']['financing']['fundDetails']:
                                    csv_record.append(financingDetails['fundInfo']['irIndex']['reference'])
                                    csv_record.append(financingDetails['fundInfo']['frequency']['name'])
                                    csv_record.append(financingDetails['fundInfo']['couponBasis']['name'])
                                    csv_record.append(financingDetails['fundInfo']['spread'])
                                    csv_record.append(financingDetails['fundInfo']['capFrequency']['name'])
                                    csv_record.append(financingDetails['fundInfo']['redemptionEndDate'])
                                    csv_record.append(financingDetails['fundInfo']['fixedRate'])

                                csv_record.append(position['fundings']['bond']['bondDetails']['reference'])
                                csv_record.append(position['fundings']['bond']['bondDetails']['name'])
                                csv_record.append(position['fundings']['bond']['bondDetails']['productCurrency'])
                                csv_record.append(position['fundings']['bond']['bondDetails']['amount'])
                                csv_record.append(position['fundings']['bond']['bondDetails']['exchange'])
                                csv_record.append(position['fundings']['bond']['bondDetails']['bondUnit'])
                                csv_record.append(position['fundings']['bond']['bondDetails']['identifer'])
                                csv_record.append(position['fundings']['bond']['bondDetails']['identiferUnit'])

                                if(position['instrumentDetail']['allotment'] == 'TRS Bonds'):


                                csv_records.append(csv_record)
                        except Exception as err:
                            csv_record.append('<<<' + str(err) + '>>>')
                    else:
                        logging.warning(position['instrumentDetail']['referenceData']['reference'] + ' has expired!')

        csv_records = sorted(csv_records, key=operator.itemgetter(0, 1))
        return csv_records
