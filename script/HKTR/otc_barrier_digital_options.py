import logging
import operator
from .default_product import default_product
from .common_util import check_nested_key
from datetime import datetime


class otc_barrier_digital_options(default_product):

    def __init__(self):
        self.__allotment__ = ['Bond Option', 'OTC Options', 'Barrier Options', 'Digital Options']
        self.__header__ = list()
        self.__header__.append('Instrument Reference')
        self.__header__.append('Portfolio')
        self.__header__.append('Trade ID')
        self.__header__.append('UTI')
        self.__header__.append('Allotment')
        self.__header__.append('Action')
        self.__header__.append('Business Event')
        self.__header__.append('Trade Date')
        self.__header__.append('Maturity Date')
        self.__header__.append('Execution DateTime')
        self.__header__.append('Initial Execution DateTime')
        self.__header__.append('Reporting Party LEI')
        self.__header__.append('NonReporting Party LEI')
        self.__header__.append('Counterparty Reference')
        self.__header__.append('Counterparty Name')
        self.__header__.append('Quantity')
        self.__header__.append('Nominal')
        self.__header__.append('Payment Date')
        self.__header__.append('Payment Currency')
        self.__header__.append('Options Premium Pay Date')
        self.__header__.append('Options Payment Date')
        self.__header__.append('Net Amount')
        self.__header__.append('Net Amount Currency')
        self.__header__.append('Notional')
        self.__header__.append('Notional Currency')
        self.__header__.append('Price Type')
        self.__header__.append('Underlying Currency')
        self.__header__.append('Underlying Reference')
        self.__header__.append('Underlying Name')
        self.__header__.append('Underlying Exchange')
        self.__header__.append('Underlying Allotment')
        self.__header__.append('Underlying Unit')
        self.__header__.append('ISIN')
        self.__header__.append('CallPut')
        self.__header__.append('Exercise Type')
        self.__header__.append('Delivery Type')
        self.__header__.append('Strike')
        self.__header__.append('Strike Currency')
        self.__header__.append('Currency1')
        self.__header__.append('Currency2')
        self.__header__.append('Exchange Rate')

    def generate_positions(self, positions, reportDate):
        csv_records = list()
        for position in positions:
            if position['instrumentDetail']['allotment'] in self.__allotment__:
                for trade in position['Trades']:
                    expiry_date = datetime.strptime(position['endDate']['date'], '%Y-%m-%d')
                    report_date = datetime.strptime(reportDate, "%Y-%m-%d")
                    #if expiry_date > report_date and position['quantity'] != 0:
                    if 1 == 1:
                        csv_record = list()
                        try:
                            lastModificationDate = trade['lastModificationDate']['date']
                            tradeResend = trade['UTI']['tradeResend']
                            if lastModificationDate <= reportDate or tradeResend:
                                previousUTI = trade['UTI']['previousUTI']
                                UTI = position['UTI']
                                if len(previousUTI) > 0:
                                    UTI = previousUTI
                                csv_record.append(position['instrumentDetail']['referenceData']['reference'])
                                csv_record.append(position['portfolio'])
                                csv_record.append(trade['ticketId'])
                                csv_record.append(position['UTI'])
                                csv_record.append(position['instrumentDetail']['allotment'])
                                csv_record.append(trade['actionType'])
                                csv_record.append(trade['businessEvent']['name'])
                                csv_record.append(position['tradeDate']['date'])
                                csv_record.append(position['endDate']['date'])
                                csv_record.append(trade['transactionDateTime']['dateTime'])
                                csv_record.append(position['initialTransactionDateTime']['dateTime'])
                                csv_record.append(trade['entityLEI'])
                                csv_record.append(trade['counterPartyLEI'])
                                csv_record.append(trade['counterparty']['reference'])
                                csv_record.append(trade['counterparty']['name'])
                                csv_record.append(trade['quantity'])
                                csv_record.append(trade['nominal'])
                                csv_record.append(trade['paymentDate']['date'])
                                csv_record.append(trade['settlementCurrency']['ISO'])
                                premiumPayDate = position['premiumPayDate']['sophisDate']
                                if premiumPayDate != 0:
                                    csv_record.append(position['premiumPayDate']['date'])
                                else:
                                    csv_record.append('')
                                csv_record.append(position['paymentDate']['date'])
                                csv_record.append(trade['notional']['amount'])
                                csv_record.append(trade['notional']['currency']['ISO'])
                                csv_record.append(trade['notional']['amount'])
                                csv_record.append(trade['notional']['currency']['ISO'])
                                csv_record.append(trade['priceType']['name'])
                                csv_record.append(position['underlying']['productCurrency']['ISO'])
                                csv_record.append(position['underlying']['referenceData']['reference'])
                                csv_record.append(position['underlying']['referenceData']['name'])
                                csv_record.append(position['underlying']['exchange'])
                                csv_record.append(position['underlying']['allotment'])
                                if check_nested_key(trade, 'underlyingUnit'):
                                    csv_record.append(trade['underlyingUnit'])
                                else:
                                    csv_record.append('')
                                if check_nested_key(position, 'underlying', 'references', 'ISIN'):
                                    csv_record.append(position['underlying']['references']['ISIN'])
                                else:
                                    csv_record.append('')
                                csv_record.append(position['callPut'])
                                csv_record.append(position['exerciseType']['name'])
                                csv_record.append(position['deliveryType']['name'])
                                csv_record.append(position['strike']['amount'])
                                csv_record.append(position['strike']['currency']['ISO'])
                                csv_record.append(trade['tradeCurrency']['ISO'])
                                csv_record.append(position['instrumentDetail']['productCurrency']['ISO'])
                                csv_record.append(trade['fxSpot'])

                                csv_records.append(csv_record)
                        except Exception as err:
                            csv_record.append('<<<' + str(err) + '>>>')
                    else:
                        logging.warning(position['instrumentDetail']['referenceData']['reference'] + ' has expired!')

        csv_records = sorted(csv_records, key=operator.itemgetter(0, 1))
        return csv_records
