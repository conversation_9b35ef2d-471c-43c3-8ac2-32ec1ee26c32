from HKTR.accu_decu import accu_decu
from HKTR.autocall_snball_fcs import autocall_snball_fcs
from HKTR.ccs_cds_irs import ccs_cds_irs
from HKTR.eq_fut_idx_trscb_swap import eq_fut_idx_trscb_swap
from HKTR.fx_spot import fx_spot
from HKTR.fxforward_ndf import fxforward_ndf
from HKTR.index_futures import index_futures
from HKTR.otc_barrier_digital_options import otc_barrier_digital_options
from HKTR.trsbonds_trsbasketbonds import trsbonds_trsbasketbonds

PRODUCTS = {
    'accu_decu': {
        'class': accu_decu,
        'description': 'Accumulators, Decumulators',
        'filename': 'accu_decu'
    },
    'autocall_snball_fcs': {
        'class': autocall_snball_fcs,
        'description': 'Autocall, SnowBall, Fixed Coupon Swap',
        'filename': 'autocall_snball_fcs'
    },
    'ccs_cds_irs': {
        'class': ccs_cds_irs,
        'description': 'Cross Currency Swap, Credit Default Swap, Interest Rate Swap',
        'filename': 'ccs_cds_irs'
    },
    'eq_fut_idx_trscb_swap': {
        'class': eq_fut_idx_trscb_swap,
        'description': 'Equity, Futures, Index, TRSCB Swap',
        'filename': 'eq_fut_idx_trscb_swap'
    },
    'fx_spot': {
        'class': fx_spot,
        'description': 'FX Spot',
        'filename': 'fx_spot'
    },
    'fxforward_ndf': {
        'class': fxforward_ndf,
        'description': 'Fx Forward, Non Deliverable Forwards',
        'filename': 'fxforward_ndf'
    },
    'index_futures': {
        'class': index_futures,
        'description': 'Index Futures',
        'filename': 'index_futures'
    },
    'otc_barrier_digital_options': {
        'class': otc_barrier_digital_options,
        'description': 'OTC Options, Barrier Options, Digital Options',
        'filename': 'otc_barrier_digital_options'
    },
    'trsbonds_trsbasketbonds': {
        'class': trsbonds_trsbasketbonds,
        'description': 'TRS Bonds, TRS Basket Bonds',
        'filename': 'trsbonds_trsbasketbonds'
    },
}
