from HKTR.accu_decu import accu_decu
from HKTR.fcs import fcs
from HKTR.ccs_cds_irs import ccs_cds_irs
from HKTR.eq_fut_idx_trscb_swap import eq_fut_idx_trscb_swap
from HKTR.fx_swap import fx_swap
from HKTR.otc_digital_options import otc_digital_options
from HKTR.trsbonds_trsbasketbonds import trsbonds_trsbasketbonds

PRODUCTS = {
    'accu_decu': {
        'class': accu_decu,
        'description': 'Accumulators, Decumulators',
        'filename': 'accu_decu'
    },
    'autocall_snball_fcs': {
        'class': fcs,
        'description': '<PERSON>call, SnowBall, Fixed Coupon Swap',
        'filename': 'fcs'
    },
    'ccs_cds_irs': {
        'class': ccs_cds_irs,
        'description': 'Cross Currency Swap, Credit Default Swap, Interest Rate Swap',
        'filename': 'ccs_cds_irs'
    },
    'eq_fut_idx_trscb_swap': {
        'class': eq_fut_idx_trscb_swap,
        'description': 'Equity, Futures, Index, TRSCB Swap',
        'filename': 'eq_fut_idx_trscb_swap'
    },
    'fx_swap': {
        'class': fx_swap,
        'description': 'FX Spot, Fx Forward, Non Deliverable Forwards',
        'filename': 'fx_spot_forward_ndf'
    },
    'otc_barrier_digital_options': {
        'class': otc_digital_options,
        'description': 'OTC Options, Barrier Options, Digital Options',
        'filename': 'otc_digital_options'
    },
    'trsbonds_trsbasketbonds': {
        'class': trsbonds_trsbasketbonds,
        'description': 'TRS Bonds, TRS Basket Bonds',
        'filename': 'trsbonds_trsbasketbonds'
    },
}
