'''
Date        Name            Detail
========================================================================================================================
20250128    Alvin Mak       Initial version to handle HKTR trade extractions.
                            Fixed Coupon Swap
'''

import logging
import operator
from .default_product import default_product
from .xml_key_util import check_nested_key
from datetime import datetime


class fcs(default_product):

    def __init__(self):
        self.__allotment__ = ['Fixed Cpn Swap']
        self.__header__ = list()
        self.__header__ = list()
        self.__header__.append('Instrument Reference')
        self.__header__.append('Portfolio')
        self.__header__.append('Trade ID')
        self.__header__.append('UTI')
        self.__header__.append('Allotment')
        self.__header__.append('Action')
        self.__header__.append('Business Event')
        self.__header__.append('Trade Date')
        self.__header__.append('Start Date')
        self.__header__.append('Maturity Date')
        self.__header__.append('Execution DateTime')
        self.__header__.append('Initial Execution DateTime')
        self.__header__.append('Reporting Party LEI')
        self.__header__.append('NonReporting Party LEI')
        self.__header__.append('Counterparty Reference')
        self.__header__.append('Counterparty Name')
        self.__header__.append('Quantity')
        self.__header__.append('Nominal')
        self.__header__.append('Payment Date')
        self.__header__.append('Payment Currency')
        self.__header__.append('Final Notional')
        self.__header__.append('Net Amount')
        self.__header__.append('Price Type')
        self.__header__.append('Paying Leg IR Index')
        self.__header__.append('Paying Leg Reset Frequency')
        self.__header__.append('Paying Leg Coupon Basis')
        self.__header__.append('Paying Leg Spread')
        self.__header__.append('Paying Leg Cap Frequency')
        self.__header__.append('Strike Price')
        self.__header__.append('Strike Currency')
        self.__header__.append('Barrier Down Strike')
        self.__header__.append('Barrier Up Coupon')
        self.__header__.append('Fixing Date')
        self.__header__.append('Underlying Reference')
        self.__header__.append('Underlying Name')
        self.__header__.append('Underlying Exchange')
        self.__header__.append('Underlying Type')
        self.__header__.append('Underlying ISIN')
        self.__header__.append('Underlying Unit')
        self.__header__.append('Underlying Past Fixing Value')
        self.__header__.append('Underlying Number of Shares')
        self.__header__.append('Underlying Weighted Avg Price')

    def generate_positions(self, positions, reportDate):
        csv_records = list()
        for position in positions:
            if position['instrumentDetail']['allotment'] in self.__allotment__:
                for trade in position['Trades']:
                    expiry_date = datetime.strptime(position['endDate']['date'], '%Y-%m-%d')
                    report_date = datetime.strptime(reportDate, "%Y-%m-%d")
                    if expiry_date > report_date and trade['quantity'] != 0:
                        csv_record = list()
                        try:
                            lastModificationDate = trade['lastModificationDate']['date']
                            tradeResend = trade['UTI']['tradeResend']
                            if lastModificationDate <= reportDate or tradeResend:
                                previousUTI = trade['UTI']['previousUTI']
                                UTI = position['UTI']
                                if len(previousUTI) > 0:
                                    UTI = previousUTI
                                csv_record.append(position['instrumentDetail']['referenceData']['reference'])
                                csv_record.append(position['portfolio'])
                                csv_record.append(trade['ticketId'])
                                csv_record.append(position['UTI'])
                                csv_record.append(position['instrumentDetail']['allotment'])
                                csv_record.append(trade['actionType'])
                                csv_record.append(trade['businessEvent']['name'])
                                csv_record.append(position['tradeDate']['date'])
                                csv_record.append(position['IssueDate']['date'])
                                csv_record.append(position['endDate']['date'])
                                csv_record.append(trade['transactionDateTime']['dateTime'])
                                csv_record.append(position['initialTransactionDateTime']['dateTime'])
                                csv_record.append(trade['entityLEI'])
                                csv_record.append(trade['counterPartyLEI'])
                                csv_record.append(trade['counterparty']['reference'])
                                csv_record.append(trade['counterparty']['name'])
                                csv_record.append(trade['quantity'])
                                csv_record.append(trade['nominal'])
                                csv_record.append(trade['paymentDate']['date'])
                                csv_record.append(position['SettlementCurrency']['ISO'])
                                csv_record.append(position['notional1']['amount'])
                                csv_record.append(trade['netAmount']['amount'])
                                csv_record.append(trade['priceType']['name'])
                                csv_record.append(position['payingLegInfo']['irIndex']['reference'])
                                csv_record.append(position['payingLegInfo']['frequency']['name'])
                                csv_record.append(position['payingLegInfo']['couponBasis']['name'])
                                csv_record.append(position['payingLegInfo']['spread'])
                                csv_record.append(position['payingLegInfo']['capFrequency']['name'])

                                clauseName = [d['ClauseName'] for d in position['PayOffs']]
                                i = 0
                                cliquet_strike = 0
                                for clauseItem1 in clauseName:
                                    if clauseItem1 == 'M_eCliquet':
                                        cliquet_strike = position['PayOffs'][i]['Strike']
                                        break
                                    else:
                                        i += 1
                                csv_record.append(cliquet_strike)
                                csv_record.append(position['instrumentDetail']['productCurrency']['ISO'])
                                j = 0
                                barrierDown_strike = 0
                                for clauseItem2 in clauseName:
                                    if clauseItem2 == 'M_eBarrierDownTrigger':
                                        barrierDown_strike = round(position['PayOffs'][j]['Strike'], 2)
                                        break
                                    else:
                                        j += 1
                                csv_record.append(barrierDown_strike)
                                k = 0
                                barrierUp_coupon = 0

                                for clauseItem3 in clauseName:
                                    if clauseItem3 == 'M_eBarrierUpTrigger':
                                        barrierUp_coupon = position['PayOffs'][k]['Coupon']
                                        break
                                    else:
                                        k += 1
                                csv_record.append(barrierUp_coupon)
                                csv_record.append(position['fixingDate']['date'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingRef'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingName'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingExchange'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingType'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingISIN'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingUnit'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingPastFixingValue'])
                                csv_record.append(trade['clauseUnderlyingInfo']['underlyingShares'])
                                csv_record.append(round(trade['clauseUnderlyingInfo']['weightedAvgPrice'], 2))

                                csv_records.append(csv_record)
                        except Exception as err:
                            print(err, trade)
                            csv_record.append('<<<' + str(err) + '>>>')
                    else:
                        logging.warning(position['instrumentDetail']['referenceData']['reference'] + ' has expired!')

        csv_records = sorted(csv_records, key=operator.itemgetter(0, 1))
        return csv_records
