'''
Date        Name            Detail
========================================================================================================================
20250128    Alvin Mak       Add Accumulators and Decumulators parser. This parser supports trade level extraction.
'''
import logging
import operator
from .default_product import default_product
from .common_util import check_nested_key
from datetime import datetime


class autocall_snball_fcn_swap(default_product):

    def __init__(self):
        self.__allotment__ = ['AutoCall Swap', 'Snball Swap', 'Fixed Cpn Swap']
        self.__header__ = list()
        self.__header__ = list()
        self.__header__.append('Instrument Reference')
        self.__header__.append('Portfolio')
        self.__header__.append('Trade ID')
        self.__header__.append('UTI')
        self.__header__.append('Allotment')
        self.__header__.append('Action')
        self.__header__.append('Business Event')
        self.__header__.append('Trade Date')
        self.__header__.append('Start Date')
        self.__header__.append('Maturity Date')
        self.__header__.append('Execution DateTime')
        self.__header__.append('Initial Execution DateTime')
        self.__header__.append('Reporting Party LEI')
        self.__header__.append('NonReporting Party LEI')
        self.__header__.append('Counterparty Reference')
        self.__header__.append('Counterparty Name')
        self.__header__.append('Quantity')
        self.__header__.append('Settlement Date')
        self.__header__.append('Settlement Currency')
        self.__header__.append('Final Notional')
        self.__header__.append('Price Type')
        self.__header__.append('Interest Leg IR Index')
        self.__header__.append('Interest Leg Frequency')
        self.__header__.append('Interest Leg Coupon Basis')
        self.__header__.append('Strike Price')
        self.__header__.append('Strike Currency')
        self.__header__.append('Barrier Down Strike')
        self.__header__.append('Barrier Up Coupon')
        self.__header__.append('First Payment Date')
        self.__header__.append('Last Payment Date')
        self.__header__.append('Fixing Date')
        self.__header__.append('First CashFlow Payment Date')
        self.__header__.append('Last CashFlowPayment Date')
        self.__header__.append('ISIN')
        self.__header__.append('Underlying Reference1')
        self.__header__.append('Underlying Name1')
        self.__header__.append('Underlying Exchange1')
        self.__header__.append('Underlying Reference2')
        self.__header__.append('Underlying Name2')
        self.__header__.append('Underlying Exchange2')
        self.__header__.append('Underlying Reference3')
        self.__header__.append('Underlying Name3')
        self.__header__.append('Underlying Exchange3')

    def generate_positions(self, positions, reportDate):
        csv_records = list()
        for position in positions:
            if position['instrumentDetail']['allotment'] in self.__allotment__:
                for trade in position['Trades']:
                    expiry_date = datetime.strptime(position['endDate']['date'], '%Y-%m-%d')
                    report_date = datetime.strptime(reportDate, "%Y-%m-%d")
                    if expiry_date > report_date and trade['quantity'] != 0:
                        csv_record = list()
                        try:
                            lastModificationDate = trade['lastModificationDate']['date']
                            tradeResend = trade['UTI']['tradeResend']
                            if lastModificationDate <= reportDate or tradeResend:
                                previousUTI = trade['UTI']['previousUTI']
                                UTI = position['UTI']
                                if len(previousUTI) > 0:
                                    UTI = previousUTI
                                csv_record.append(position['instrumentDetail']['referenceData']['reference'])
                                csv_record.append(position['portfolio'])
                                csv_record.append(trade['ticketId'])
                                csv_record.append(position['UTI'])
                                csv_record.append(position['instrumentDetail']['allotment'])
                                csv_record.append(trade['actionType'])
                                csv_record.append(trade['businessEvent']['name'])
                                csv_record.append(position['tradeDate']['date'])
                                csv_record.append(position['IssueDate']['date'])
                                csv_record.append(position['endDate']['date'])
                                csv_record.append(trade['transactionDateTime']['dateTime'])
                                csv_record.append(position['initialTransactionDateTime']['dateTime'])
                                csv_record.append(trade['entityLEI'])
                                csv_record.append(trade['counterPartyLEI'])
                                csv_record.append(trade['counterparty']['reference'])
                                csv_record.append(trade['counterparty']['name'])
                                csv_record.append(trade['quantity'])
                                csv_record.append(trade['settlementDate']['date'])
                                csv_record.append(position['SettlementCurrency']['ISO'])
                                csv_record.append(position['notional1']['amount'])
                                csv_record.append(trade['priceType']['name'])
                                csv_record.append(position['payingLegInfo']['irIndex']['reference'])
                                csv_record.append(position['payingLegInfo']['frequency']['name'])
                                csv_record.append(position['payingLegInfo']['couponBasis']['name'])
                                clauseName = [d['ClauseName'] for d in position['PayOffs']]
                                i = 0
                                cliquet_strike = 0
                                for clauseItem1 in clauseName:
                                    if clauseItem1 == 'M_eCliquet':
                                        cliquet_strike = position['PayOffs'][i]['Strike']
                                        break
                                    else:
                                        i += 1
                                csv_record.append(cliquet_strike)
                                csv_record.append(position['instrumentDetail']['productCurrency']['ISO'])
                                j = 0
                                barrierDown_strike = 0
                                for clauseItem2 in clauseName:
                                    if clauseItem2 == 'M_eBarrierDownTrigger':
                                        barrierDown_strike = round(position['PayOffs'][j]['Strike'], 2)
                                        break
                                    else:
                                        j += 1
                                csv_record.append(barrierDown_strike)
                                k = 0
                                barrierUp_coupon = 0
                                for clauseItem3 in clauseName:
                                    if clauseItem3 == 'M_eBarrierUpTrigger':
                                        barrierUp_coupon = position['PayOffs'][k]['Coupon']
                                        break
                                    else:
                                        k += 1
                                csv_record.append(barrierUp_coupon)
                                l = 0
                                for clauseItem4 in clauseName:
                                    if clauseItem4 == 'M_eBarrierUpTrigger':
                                        first_paydate = position['PayOffs'][l]['PayDate']['date']
                                        break
                                    else:
                                        l += 1
                                csv_record.append(first_paydate)
                                m = 0
                                for clauseItem5 in clauseName:
                                    if clauseItem5 == 'M_eBarrierUpTrigger':
                                        last_paydate = position['PayOffs'][m]['PayDate']['date']
                                    else:
                                        m += 1
                                csv_record.append(last_paydate)
                                csv_record.append(position['fixingDate']['date'])
                                csv_record.append(position['firstCashPayDate']['date'])
                                csv_record.append(position['lastCashPayDate']['date'])
                                if check_nested_key(position, 'ClauseUnderlying', 'references', 'ISIN'):
                                    csv_record.append(position['ClauseUnderlying']['references']['ISIN'])
                                else:
                                    csv_record.append("")
                                for underlying in position['ClauseUnderlying']:
                                    csv_record.append(underlying['referenceData']['reference'])
                                    csv_record.append(underlying['referenceData']['name'])
                                    csv_record.append(underlying['exchange'])

                                csv_records.append(csv_record)
                        except Exception as err:
                            print(err, trade)
                            csv_record.append('<<<' + str(err) + '>>>')
                    else:
                        logging.warning(position['instrumentDetail']['referenceData']['reference'] + ' has expired!')

        csv_records = sorted(csv_records, key=operator.itemgetter(0, 1))
        return csv_records
