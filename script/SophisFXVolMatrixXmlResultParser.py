#
# files:
# SophisFXVolMatrixXmlResultParser.py
# SophisFXVolMatrixXmlResultParser.properties
#
#
# Date        Name            Comment
# ============================================================================================
# 20210623    Yvonne Yeung    Develope python program to convert sophis xml scenario result file to excel (.xlsx) for FXVolMatrix
# 20210728    Yvonne Yeung    Update sql to extract previous date spot price
# 20240525    Alvin Mak       Enhance to support relative date calculations generated from Sophis.

from collections import OrderedDict

from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import StringUtil
import sys

import traceback
import xlsxwriter

import xmltodict
import re

import cx_Oracle
import datetime
import sys
import os


# get result line value by underlying reference strike and maturity
def FilterResultLines(ResultLines, UnderlyingRef, InstrumentStrike, MaturityDate):
    Result = ''
    try:
        for ResultLine in ResultLines:
            Maturity = ResultLine.get('@maturity')
            ResultLinetext = ResultLine.get('#text')
            Strike = ResultLine.get('@strike')
            underlying_ref = ResultLine.get('@underlying-ref')
            if MaturityDate == Maturity and UnderlyingRef == underlying_ref and InstrumentStrike == Strike:
                Result = ResultLinetext
                break
    except Exception as e:
        logger.error('%s', traceback.format_exc())

    return Result


# get instrument spot price from historique table by date (tenor) and instrument code
def GetSpot(InstrumentCode, tenor):
    Result = 0.0
    # Script = 'select d from historique where jour = to_date(:tenor, \'YYYY-MM-DD\') and sicovam= :instrument_code'
    Script = 'select d from (select d from historique where jour < to_date(:tenor, \'YYYY-MM-DD\') and sicovam= :instrument_code order by jour desc) WHERE ROWNUM = 1'
    try:
        SophisDb = SophisDBConnection()
        with cx_Oracle.connect(SophisDb.getUserId(), SophisDb.getPassword(),
                               SophisDb.getHost() + "/" + SophisDb.getDbInstance()) as dbConn:
            logger.info('Query Spot: %s , %s, %s ', Script, str(InstrumentCode), str(tenor))
            with dbConn.cursor() as cursor:
                """ execute restore script"""
                cursor.execute(Script, tenor=tenor, instrument_code=InstrumentCode)
                sql_result = cursor.fetchall()
                if cursor.rowcount > 0:
                    Result = sql_result[0][0]
    except Exception as e:
        logger.error('%s', traceback.format_exc())

    return Result


# get a list of UnderlyingCode,UnderlyingRef pair from
# scenario:scenario/scenario:results/scenario:book/scenario:position
# reporting:root/reporting:scenarioSource/reporting:scenario/scenario:results/scenario:book/scenario:global/scenario:marketData
# scenario:results/scenario:book/scenario:global/scenario:marketData
def GetUnderlyingList(ResultsXMLTag):
    UnderlyingList = []
    try:
        if ResultsXMLTag.get('book').get('scenario:error') is None:
            # Underlying
            # scenario:scenario/scenario:results/scenario:book/scenario:position
            Positions = ResultsXMLTag.get('book').get('position')
            logger.info('Positions Type = %s', type(Positions))
            if isinstance(Positions, dict):
                for Position in Positions:
                    # scenario:scenario/scenario:results/scenario:book/scenario:position/scenario:resultLine
                    logger.info('Position Type = %s', type(Position))
                    if isinstance(Position, dict):
                        PositionResultLines = Position.get('resultLine')
                        for PositionResultLine in PositionResultLines:
                            logger.info('dict Type = %s', type(PositionResultLine))
                            if isinstance(PositionResultLine, dict):
                                # scenario:underlying-ref
                                UnderlyingRef = PositionResultLine.get('@underlying-ref')
                                UnderlyingCode = PositionResultLine.get('@underlying-id')
                                if [UnderlyingCode, UnderlyingRef] not in UnderlyingList:
                                    UnderlyingList.append([UnderlyingCode, UnderlyingRef])

            # reporting:root/reporting:scenarioSource/reporting:scenario/scenario:results/scenario:book/scenario:global/scenario:marketData
            # scenario:results/scenario:book/scenario:global/scenario:marketData
            Global = ResultsXMLTag.get('book').get('global')
            logger.info('Global Type = %s', type(Global))
            if isinstance(Global, dict):
                MarketDatas = Global.get('marketData')
                for MarketData in MarketDatas:
                    logger.info('MarketData Type = %s', type(MarketData))
                    if isinstance(MarketData, dict):
                        # scenario:underlying-ref
                        UnderlyingRef = MarketData.get('@underlying-ref')
                        UnderlyingCode = MarketData.get('@underlying-id')
                        if [UnderlyingCode, UnderlyingRef] not in UnderlyingList:
                            UnderlyingList.append([UnderlyingCode, UnderlyingRef])

    except Exception as e:
        logger.error('%s', traceback.format_exc())
    return UnderlyingList


# Convert the relative XML source Enum to the abbreviation Enum. That is Month->m, Week->w, Year-> y
def GetPeriodAbbrevEnum(PeriodEnum):
    PeriodAbbrevEnum = []

    for i in range(len(PeriodEnum)):
        if PeriodEnum[i] == 'Month':
            PeriodAbbrevEnum.insert(i, 'm')
        elif PeriodEnum[i] == 'Week':
            PeriodAbbrevEnum.insert(i, 'w')
        elif PeriodEnum[i] == 'Year':
            PeriodAbbrevEnum.insert(i, 'y')
        else:
            raise Exception('Undefined PeriodAbbrevEnum!')
    return PeriodAbbrevEnum


""" program start """
logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
try:
    """ set today """
    today = datetime.date.today()

    """ read parameters """
    BusinessDate = PropertiesUtil.configObject.GetConfigProperties()['Others'].get('BusinessDate')
    InputFile = ''
    OutputFile = ''
    IsRelativeDate = False

    ConfigInputFile = 'InputFile='
    ConfigOutputFile = 'OutputFile='
    ConfigIsRelativeDate = 'IsRelativeDate='
    logger.info('sys.argv= %s', str(sys.argv))

    for argument in sys.argv:
        if argument.startswith(ConfigInputFile):
            InputFile = argument.replace(ConfigInputFile, "")
        elif argument.startswith(ConfigOutputFile):
            OutputFile = argument.replace(ConfigOutputFile, "")
        elif argument.startswith(ConfigIsRelativeDate):
            IsRelativeDate = argument.replace(ConfigIsRelativeDate, "")

    if OutputFile == '':
        OutputFile = InputFile.replace('.xml', '.xlsx')

    logger.info('InputFile = %s', InputFile)
    logger.info('OutputFile = %s', OutputFile)
    logger.info('RelativeDate = %s', IsRelativeDate)

    """ format date """
    if BusinessDate != '':
        today = datetime.datetime.strptime(BusinessDate, '%Y%m%d')
        logger.info('Set today = %s', BusinessDate)

    # parse an xml file by name
    f = open(InputFile, "r")
    XMLData = f.read()

    namespace = {'http://www.w3.org/1999/XSL/Transform': None,
                 'http://www.sophis.net/reporting': None,
                 'http://sophis.net/sophis/common': None,
                 'http://www.sophis.net/folio': None,
                 'http://www.sophis.net/Instrument': None,
                 'http://www.sophis.net/scenario': None,
                 'http://www.w3.org/2001/XMLSchema-instance': None
                 }

    result = xmltodict.parse(XMLData,
                             process_namespaces=True,
                             namespaces=namespace)

    # Get distinct underlying/ strike and maturity date
    Parameters = {}
    Results = {}
    Positions = {}
    Scenario = {}

    if result.get('root'):
        # reporting:root/reporting:scenarioSource/reporting:scenario/
        Scenario = result.get('root').get('scenarioSource').get('scenario')
    else:
        # scenario:scenario/
        Scenario = result.get('scenario')

    # scenario:scenario/scenario:parameters
    Parameters = Scenario.get('parameters')
    # scenario:scenario/scenario:results
    Results = Scenario.get('results')

    # Calculation Date
    Tenor = list(Parameters.get('tenor').values())[0]
    if Tenor == '1904-01-01':   # Default date generated from Sophis
        Tenor = today.strftime('%Y%m%d')
    logger.info('Set tenor as today = %s', Tenor)

    # Strike
    # scenario:scenario/scenario:parameters/scenario:strike
    Strikes = Parameters.get('strike')
    Strikes.sort(key=float)
    logger.info('Strikes = %s', Strikes)

    # Maturity
    # scenario:scenario/scenario:parameters/scenario:maturity
    MaturityCollection = Parameters.get('maturity')

    # Sophis input file is absolute dates
    if not IsRelativeDate:
        Maturities = [list(d.values())[0] for d in MaturityCollection]
        Maturities.sort()
    # Sophis input file is relative dates
    else:
        # scenario:scenario/scenario:parameters/scenario:maturity/scenario:relativeDate/
        RelativeDateCollection = [list(d1.values())[0] for d1 in MaturityCollection]
        # scenario:scenario/scenario:parameters/scenario:maturity/scenario:relativeDate/common:periodMultiplier
        PeriodMultiplier = [list(d2.values())[0] for d2 in RelativeDateCollection]
        # scenario:scenario/scenario:parameters/scenario:maturity/scenario:relativeDate/common:periodEnum
        PeriodEnum = [list(d2.values())[1] for d2 in RelativeDateCollection]
        # Convert the source Enum to the abbreviation Enum. That is Month->m, Week->w, Year-> y
        PeriodAbbrevEnum = GetPeriodAbbrevEnum(PeriodEnum)

        PeriodMultiplier = sorted(PeriodMultiplier, key=lambda x: int(x))
        logger.info('PeriodMultiplier = %s', PeriodMultiplier)

        # Concatenate the relative date PeriodMultiplier and PeriodAbbrevEnum. Eg. 1m, 2m, 3m,...
        PeriodMultiplierEnum = list(map("".join, zip(PeriodMultiplier, PeriodAbbrevEnum)))
        Maturities = PeriodMultiplierEnum

    logger.info('Maturities = %s', Maturities)

    if Results.get('book').get('scenario:error') is None:
        # Underlying
        UnderlyingList = GetUnderlyingList(Results)
        UnderlyingListSorted = sorted(UnderlyingList, key=lambda x: x[1])
        logger.info('UnderlyingList = %s', UnderlyingListSorted)

        # write to excel
        # reporting:root/reporting:scenarioSource/reporting:scenario/scenario:results/scenario:book/scenario:global/scenario:marketData
        # scenario:results/scenario:book/scenario:global/scenario:marketData
        Global = Results.get('book').get('global')
        GlobalResultLines = []
        if isinstance(Global, dict):
            GlobalResultLines = Global.get('resultLine')
            logger.info('GlobalResultLines Type = %s', type(GlobalResultLines))
        GlobalResultLinesSorted = []

        if isinstance(GlobalResultLines, list):
            GlobalResultLinesSorted = sorted(GlobalResultLines,
                                             key=lambda x: (
                                                 x.get('@underlying-ref'), float(x.get('@strike')), x.get('@maturity')))

        # for each underlying
        logger.info('write to excel = %s', OutputFile)
        Workbook = xlsxwriter.Workbook(OutputFile)
        for Underlying in UnderlyingListSorted:
            SheetName = re.sub(r'[\/\\\*\[\]\:\?]', '_', Underlying[1])
            SheetName = SheetName[0: 32]

            # get sheet name (underlying reference)
            logger.info('SheetName = %s', SheetName)
            Worksheet = Workbook.add_worksheet(name=SheetName)

            # header row (,,,,MATURITY DATE 1,MATURITY DATE 2...,Grand Total)
            row = 0
            col = 0
            ColTotal = []
            Worksheet.write(row, 1, '$Strike')
            for MaturityDate in Maturities:
                Worksheet.write(row, col + 4, MaturityDate)
                ColTotal.append(0)
                col += 1
            Worksheet.write(row, col + 4, 'Grand Total')
            row += 1

            # get spot from database
            UnderlyingCurrentSpot = GetSpot(Underlying[0], Tenor)
            logger.info('Underlying Current Spot = %s', UnderlyingCurrentSpot)

            # content ('Call/Put',underlying reference,spot,strike,value 1,value 2,..., sum of the row)
            for Strike in Strikes:
                col = 0

                UnderlyingTargetSpot = round(float(Strike) / 100 * UnderlyingCurrentSpot, 2)

                Worksheet.write(row, 0, 'Call/Put')
                Worksheet.write(row, 1, UnderlyingTargetSpot)
                Worksheet.write(row, 2, Underlying[1])
                Worksheet.write(row, 3, round(float(Strike), 2))

                rowTotal = 0.0
                for MaturityDate in Maturities:
                    ResultLineValue = ''
                    if GlobalResultLinesSorted:
                        ResultLineValue = FilterResultLines(GlobalResultLinesSorted, Underlying[1], Strike,
                                                            MaturityDate)
                    if ResultLineValue == '':
                        Worksheet.write(row, col + 4, '')
                    else:
                        Worksheet.write(row, col + 4, round(float(ResultLineValue)))
                        rowTotal += float(ResultLineValue)
                        ColTotal[col] += float(ResultLineValue)
                    col += 1

                if round(float(rowTotal)) != 0:
                    Worksheet.write(row, col + 4, round(float(rowTotal)))
                row += 1

            # last row
            Worksheet.write(row, 0, 'Call/Put')
            Worksheet.write(row, 1, '')
            Worksheet.write(row, 2, Underlying[1])
            Worksheet.write(row, 3, 'Total')
            col = 0
            ColRowTotal = 0
            for item in ColTotal:
                if round(float(item)) != 0:
                    Worksheet.write(row, col + 4, round(item))
                ColRowTotal += item
                col += 1
            if round(ColRowTotal) == 0:
                Worksheet.write(row, col + 4, '')
            else:
                Worksheet.write(row, col + 4, round(ColRowTotal))

        # close workbook
        Workbook.close()
        logger.info("======================================================================================")

except Exception as e:
    logger.error('%s', traceback.format_exc())
