from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import StringUtil

import cx_Oracle
import datetime
import sys
import os

""" program start """
logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
try:
    """ set today """
    today = datetime.date.today()

    """ read parameters """
    BackupScriptName = PropertiesUtil.configObject.GetConfigProperties()['Scripts'].get('BackupScriptName')
    BackupScriptOutPath = PropertiesUtil.configObject.GetConfigProperties()['Scripts'].get('BackupScriptPath')
    BusinessDate = PropertiesUtil.configObject.GetConfigProperties()['Others'].get('BusinessDate')
    logger.info('BackupScriptName = %s', BackupScriptName)
    logger.info('BackupScriptOutPath = %s', BackupScriptOutPath)
    logger.info('BusinessDate = %s', BusinessDate)

    """ format date """
    if BusinessDate != '':
        today = datetime.datetime.strptime(BusinessDate, '%Y%m%d')
        logger.info('Set today = %s', BusinessDate)

    BackupScriptName = StringUtil.formatDate(BackupScriptName, today)
    BackupScriptOutPath = StringUtil.formatDate(BackupScriptOutPath, today)
    BackupScriptFullOutPath = os.path.join(BackupScriptOutPath, BackupScriptName)
    logger.info('BackupScriptFullOutPath = %s', BackupScriptFullOutPath)

    """ read script """
    with open(BackupScriptFullOutPath, 'r') as file:
        BackupScripts = file.read().splitlines()
        logger.info('BackupScript = %s', BackupScripts)
        file.close()

    """ connection to the database """
    sophisDb = SophisDBConnection()
    with cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance()) as dbConn:
        logger.info(dbConn)

        with dbConn.cursor() as cursor:
            """ execute restore script"""
            for line in BackupScripts:
                if line != '':
                    logger.info('insert line :' + line)
                    cursor.execute(line)
                    dbConn.commit()
                    logger.info('Record inserted. %s row affected', cursor.rowcount)
except Exception as e:
    logger.info('%s', str(e))
