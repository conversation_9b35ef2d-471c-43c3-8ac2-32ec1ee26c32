## Date         Name                       Detail
# This is a python report to present the total notional amount of Equity Swap for US client reporting on daily basis.
#
# ==========================================================================================================================
# 20230306      Alvin Mak                 Initial version
# 20231127      Alvin <PERSON>                 Extend the child class to support the Equity Swap notional summary.
# 20240225      Alvin Mak                 Add SQLRecord parameter to count if transactions exist.
# 20250404      <PERSON>han<PERSON> to support total notional summary and the counterparty summary.
#

from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from ISDUSReportNotionalSummary import NotionalSummary

dateFormat = 'YYYYMMDD'

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

# Report file
template1 = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template1')
template2 = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template2')
SQLRecord = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQLRecord')
SQLSum1 = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQLSum1')
SQLSum2 = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQLSum2')
outputFilePrefix = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('LogFileSuffix')
defaultFX = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Default_USDCNH_FX')
thresholdAmt = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('ThresholdAmt')

logger.info('SQLRecord = %s', SQLRecord)
logger.info('SQLSum1 = %s', SQLSum1)
logger.info('SQLSum2 = %s', SQLSum2)


class EquitySwapNotional(NotionalSummary):
    def __init__(self, template1, template2, SQLRecord, SQLSum1, SQLSum2, outputFilePrefix, defaultFX, thresholdAmt):
        logger.info('FuturesSwapNotional Child Class: __init__')
        self.template1 = template1
        self.template2 = template2
        self.SQLRecord = SQLRecord
        self.SQLSum1 = SQLSum1
        self.SQLSum2 = SQLSum2
        self.outputFilePrefix = outputFilePrefix
        self.defaultFX = defaultFX
        self.thresholdAmt = thresholdAmt
        super().__init__(template1, template2, SQLRecord, SQLSum1, SQLSum2, outputFilePrefix, defaultFX, thresholdAmt)

    def main(self):
        logger.info('EquitySwapNotional Child Class: main()')
        super().main()


# Instantiate the Equity Swap child class
equity_swap_notional = EquitySwapNotional(template1, template2, SQLRecord, SQLSum1, SQLSum2, outputFilePrefix, defaultFX, thresholdAmt)
equity_swap_notional.main()
