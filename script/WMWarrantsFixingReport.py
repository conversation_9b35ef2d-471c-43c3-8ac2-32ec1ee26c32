## Date         Name                       Detail
##==========================================================================================================================
##20201218      Chris <PERSON>u                 Generate the warrants fixing report for WM
##20210111      Chris <PERSON>u                 Add additional columns
#                                           -Instrument
#                                           -Underlying Reference
#                                           -Derivative Type
#                                           -Strike
#                                           -Maturity
#                                           -First Fixing Date
#                                           -No Of Fixing days left
#                                           -Quantity
#                                           -Global Delta
#                                           -Global Gamma
# 20210203      Chris <PERSON>                 Move the Underlying Ticker column beside the Global Delta
# 20220330      Chris <PERSON>u                 Send out the email if there is no fixing
#
from GTJA.SophisDBConnection import SophisDBConnection
from Sophis.SophisPortfolioMap import SophisPortfolioMap
from GTJA.GTJAFormatter import GTJAFormatter
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import cx_Oracle
import datetime
from GTJA import GTJAEmailSender

dateFormat = "YYYYMMDD"
decimal_point = 2

def getPortfolio(files, businessDate):
    fileList = files.split(';')
    portfolio = dict()
    for file in fileList:
        filename = file.replace('YYYYMMDD', businessDate)
        deltaValues = SophisPortfolioMap(filename)
        portfolio.update(deltaValues.getPortfolioMap())
    return portfolio

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Header')
businessDate = datetime.date.today().strftime('%Y%m%d')

#Report file
reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFolder')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')

portfolioFile = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('deltaFiles')
portfolioMap = getPortfolio(portfolioFile, businessDate)

logger.info('Portfolio Map record')
logger.debug(portfolioMap)

SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')

#today_str = datetime.date.today().strftime('%Y%m%d')
logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', businessDate)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()
with open(SQL) as file: sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))

outputReport = ''

if (len(results) > 0):
    for internalref in results:
        sicovam = internalref[0]
        gtjaRef = internalref[1]
        firstFixingDate = internalref[2]
        name = internalref[3]
        strike = internalref[4]
        maturityDate = internalref[5]
        callPut = internalref[6]
        underlyer = internalref[7]
        remainingFixingDays = internalref[8]
        sophisRecord = portfolioMap[name]
        logger.debug('sicovam [%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s]', sicovam, gtjaRef, firstFixingDate, name, strike, maturityDate, callPut, underlyer, remainingFixingDays, sophisRecord.quantity, sophisRecord.globalDelta, sophisRecord.globalGamma)
        logger.info(sophisRecord)
        try:
            quantityInNum = float(sophisRecord.quantity.strip())
        except Exception as e:
            logger.error('%s %s', e, sophisRecord.quantity)
            quantityInNum = 0

        outputReport += '<tr>'
        outputReport += '<td>' + name + '</td>'
        outputReport += '<td>' + callPut + '</td>'
        outputReport += '<td id=\'num\'>' + "{:.2f}".format(strike) + '</td>'
        outputReport += '<td id=\'num\'>' + maturityDate.strftime('%d-%b-%Y') + '</td>'
        outputReport += '<td id=\'num\'>' + firstFixingDate.strftime('%d-%b-%Y') + '</td>'
        outputReport += '<td id=\'num\'>' + str(remainingFixingDays) + '</td>'
        outputReport += '<td id=\'num\'>' + "{:.0f}".format(quantityInNum) + '</td>'
        outputReport += '<td>' + underlyer + '</td>'
        try:
            deltaInNum = float(sophisRecord.globalDelta.strip())
            deltaInNum = round(deltaInNum, decimal_point)
            if ( deltaInNum < 0 ):
                startTag = '<td id=\'neg\'>'
            else:
                startTag = '<td>'

            outputReport += startTag + GTJAFormatter.formatAmount(deltaInNum, dp=decimal_point) + '</td>'
            dailyDelta = deltaInNum / remainingFixingDays
            outputReport += startTag + GTJAFormatter.formatAmount(dailyDelta, dp=decimal_point) + '</td>'
        except Exception as e:
            logger.error('error : %s %s', sophisRecord.globalDelta, e)
            outputReport += '<td></td>'
            outputReport += '<td></td>'
        try:
            gammaInNum = float(sophisRecord.globalGamma.strip())
            gammaInNum = round(gammaInNum, decimal_point)
            if ( gammaInNum < 0 ):
                startTag = '<td id=\'neg\'>'
            else:
                startTag = '<td>'
            outputReport += startTag + GTJAFormatter.formatAmount(gammaInNum, dp=decimal_point) + '</td>'
        except Exception as e:
            logger.error('error : %s %s', sophisRecord.globalGamma, e)
            outputReport += '<td></td>'
else:
    outputReport += '<tr><td colspan=11>No Fixing</td></tr>'

with open(template) as file:
    templateContent = file.read()
    content = templateContent.replace('###Template###', outputReport)
    outputfile = open('output/WMWarrantsFixingReport.html', 'w')
    logger.info(content)
    outputfile.write(content)
    outputfile.close()
    GTJAEmailSender.sendEmail(content, businessDate, dateFormat)


