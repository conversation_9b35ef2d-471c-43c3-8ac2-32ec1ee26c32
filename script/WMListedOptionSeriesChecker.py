from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import GTJAEmailSender
import cx_Oracle
from datetime import timedelta
import datetime



dateFormat = "YYYYMMDD"

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

businessDate = datetime.date.today().strftime('%Y%m%d')

SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
upperBound=PropertiesUtil.configObject.GetConfigProperties()['Input'].get('upperBound')
lowerBound=PropertiesUtil.configObject.GetConfigProperties()['Input'].get('lowerBound')

maturityFactor=PropertiesUtil.configObject.GetConfigProperties()['Input'].get('maturityFactor')

expectedMaturity = datetime.date.today() + timedelta(int(maturityFactor))

logger.info('Expected Maturity %s', expectedMaturity.strftime('%Y%m%d'))

logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', businessDate)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()
with open(SQL) as file: sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql, today=businessDate)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))
currentSeries = None
lastMaturityDate = None
seriesMaturityTooShort = dict()
outputRpt = "";
for record in results:
    idx = 0
    seriesCode = record[idx]
    idx+=1
    sophisUnderlyerKey = record[idx]
    idx+=1
    seriesMarketCode = record[idx]
    idx+=1
    seriesName = record[idx]
    idx+=1
    underlyerName = record[idx]
    idx+=1
    min_strike = record[idx]
    idx+=1
    max_strike = record[idx]
    idx+=1
    maturityCode = record[idx]
    idx+=1
    maturityDate = record[idx]
    idx+=1
    lastPrice = record[idx]
    idx+=1
    underlyerTicker = record[idx]
    if (underlyerTicker == None ):
        underlyerTicker = seriesName

    if currentSeries == None:
        currentSeries = seriesName
    elif currentSeries != seriesName:
        if lastMaturityDate.date() <= expectedMaturity:
            #logger.info("Maturity Date range too short %s [%s] [%s] ", currentSeries, lastMaturityDate.strftime('%Y%m%d'), expectedMaturity.strftime('%Y%m%d'))
            seriesMaturityTooShort[currentSeries] = lastMaturityDate
        else:
            #logger.info("Maturity Date range fine %s [%s] [%s] ", currentSeries, lastMaturityDate.strftime('%Y%m%d'), expectedMaturity.strftime('%Y%m%d'))
            currentSeries = seriesName

    lastMaturityDate = maturityDate

    if ( min_strike > lastPrice * float(lowerBound) or max_strike < lastPrice * float(upperBound)):
        logger.info(' price out of range [%s %s %s %s %s %s] [lower %s upper %s]', seriesName, underlyerTicker, str(min_strike), str(max_strike), str(lastPrice), maturityDate.strftime('%Y%m%d'), str(lastPrice*float(lowerBound)), str(lastPrice*float(upperBound)))
        outputRpt += '<tr>'
        outputRpt += '<td>' + seriesName + '</td>'
        outputRpt += '<td>' + underlyerTicker + '</td>'
        outputRpt += '<td>' + str(lastPrice) + '</td>'
        outputRpt += '<td>' + str(min_strike) + '</td>'
        outputRpt += '<td>' + str(max_strike) + '</td>'
        outputRpt += '<td>' + maturityDate.strftime('%d-%b-%Y') + '</td>'
        outputRpt += '</tr>\r\n'
    else:
        logger.info(' price check OK [%s %s %s %s %s %s] [lower %s upper %s]', seriesName, underlyerTicker, str(min_strike), str(max_strike), str(lastPrice), maturityDate.strftime('%Y%m%d'), str(lastPrice*float(lowerBound)), str(lastPrice*float(upperBound)))

maturityDateTooShortOutput = ""
if ( len(seriesMaturityTooShort) > 0 ):
    maturityDateTooShortOutput +='<tr bgcolor=\\''#9acd32\\''>'
    maturityDateTooShortOutput +='<th>Series</th><th>Last Maturity Date</th>'
    maturityDateTooShortOutput +='</tr>'
    for key in seriesMaturityTooShort.keys():
        logger.info("%s %s", key, str(seriesMaturityTooShort[key]))
        maturityDateTooShortOutput +='<tr>'
        maturityDateTooShortOutput +='<td>'+key+'</td><td>'+seriesMaturityTooShort[key].strftime('%d-%b-%Y')+'</td>'
        maturityDateTooShortOutput +='</tr>'

logger.info('%d %d', len(seriesMaturityTooShort), len(outputRpt))
if ( len(seriesMaturityTooShort) > 0 or len(outputRpt) > 0):
	with open(template) as file:
		templateContent = file.read()
		content = templateContent.replace('###Template###', outputRpt)
		content = content.replace('<tr></tr>', maturityDateTooShortOutput)
		outputfile = open('output/WMListedOptionSeriesChecker.html', 'w')
		outputfile.write(content)
		outputfile.close()
		GTJAEmailSender.sendEmail(content, businessDate, dateFormat)