from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import StringUtil

import cx_Oracle
import sys

""" program start """
logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
try:

    """ Parameter 1 is the script to execute"""
    path = sys.argv[2] if len(sys.argv) > 1 else 'NA'

    if path.upper().endswith('.SQL'):
        """ read the sql file and query the database"""
        Script = StringUtil.readSqlFromFile(path, 0)

        logger.info('script=' + Script)

        sophisDb = SophisDBConnection()
        with cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                               sophisDb.getHost() + "/" + sophisDb.getDbInstance()) as dbConn:
            logger.info(dbConn)
            with dbConn.cursor() as cursor:
                """ execute restore script"""
                cursor.execute(Script)
                dbConn.commit()
                logger.info('%s row affected', cursor.rowcount)

except Exception as e:
    logger.info('%s', str(e))
