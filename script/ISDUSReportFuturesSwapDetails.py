## Date         Name                       Detail
# This is a python report to check the Futures Swap notional details for US client reporting.
# Email alert is sent when the targeted threshold rate is reached.
# ==========================================================================================================================
# 20250324      Alvin Mak                 Initial version
#

from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from ISDUSReportDetails import NotionalDetails
from GTJA import StringUtil
import datetime
import os

dateFormat = 'YYYYMMDD'

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

dateFormat = 'YYYYMMDD'
today = datetime.date.today()
logger.info('today = %s', today)
businessDate = datetime.date.today().strftime('%Y%m%d')

header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Header')
businessDate = datetime.date.today()
businessDate = businessDate.strftime('%Y%m%d')

# Report file
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
SQLRecord = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQLRecord')
SQLDetail = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQLDetail')
outputFilePrefix = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('LogFileSuffix')
defaultFX = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Default_USDCNH_FX')

ReportPath = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportPath')
ReportExcelFileName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportExcelFileName')
ReportHTMLFileName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportHTMLFileName')

logger.info('SQLRecord = %s', SQLRecord)
logger.info('SQLDetail = %s', SQLDetail)

ExcelReportFullOutPath = os.path.join(ReportPath, ReportExcelFileName)
logger.info('ExcelReportFullOutPath = %s', ExcelReportFullOutPath)

HTMLReportFullOutPath = os.path.join(ReportPath, ReportHTMLFileName)
logger.info('HTMLReportFullOutPath = %s', HTMLReportFullOutPath)


class FuturesSwapDetails(NotionalDetails):
    def __init__(self, template, SQLRecord, SQLDetail, outputFilePrefix, defaultFX,
                 ExcelReportFullOutPath, HTMLReportFullOutPath):
        logger.info('FuturesSwapDetails Child Class: __init__')
        self.template = template
        self.SQLRecord = SQLRecord
        self.SQLDetail = SQLDetail
        self.outputFilePrefix = outputFilePrefix
        self.defaultFX = defaultFX
        self.ExcelReportFullOutPath = ExcelReportFullOutPath
        self.HTMLReportFullOutPath = HTMLReportFullOutPath
        super().__init__(template, SQLRecord, SQLDetail, outputFilePrefix, defaultFX,
                         ExcelReportFullOutPath, HTMLReportFullOutPath)

    def main(self):
        logger.info('FuturesSwapDetails Child Class: main()')
        super().main()


# Instantiate the Futures Swap child class
futures_swap_details = FuturesSwapDetails(template, SQLRecord, SQLDetail, outputFilePrefix, defaultFX,
                                          ExcelReportFullOutPath, HTMLReportFullOutPath)
futures_swap_details.main()
