## Date         Name                       Detail
# This is a python report to extract the daily trading volume and send by email.
#
#==========================================================================================================================
# 20220803      Chris Chiu                 First version
#
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import cx_Oracle
import datetime
import csv

dateFormat = "YYYYMMDD"
decimal_point = 4

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Header')
outputFile = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('OutputFileName')
reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('reportFolder')

csvHeaderList = list(header.split(','))

businessDate = datetime.date.today()
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')

logger.info('SQL = %s', SQL)
with open(SQL) as file: sql = file.read()

logger.info('Report Date = %s', businessDate)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance())

businessDate = businessDate.strftime('%Y%m%d')
outputFile = outputFile.replace('YYYYMMDD', businessDate)
logger.info(dbConn)
cursor = dbConn.cursor()
cursor.execute(sql)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))
outputFn = open(reportFolder + '/' + outputFile, mode='w', newline='')
csvWriter = csv.writer(outputFn, dialect='excel', quoting=csv.QUOTE_NONNUMERIC)
csvWriter.writerow(csvHeaderList)

if (len(results) > 0):
    for internalref in results:
        logger.info(internalref[0]+','+internalref[1].strftime('%Y%m%d') +','+ str(internalref[2]))
        csvWriter.writerow(internalref)