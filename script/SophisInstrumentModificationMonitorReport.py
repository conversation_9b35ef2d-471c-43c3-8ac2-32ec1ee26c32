#
# Date          Name                    Detail
# ==============================================================================================================
# 20220921      Raymond Lin             Initial commit
# 20221003      <PERSON>u              Add a queryDate so if the SophisInstrumentModificationMonitorReportSeekPos file is not available,
#                                       then it uses the queryDate instead of 1904-01-01
# 20221014      Raymond Lin             Update report columns

import os.path
import sys
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import GTJAEmailSender
import cx_Oracle
import datetime
import smtplib
from email.message import EmailMessage

DATE_FORMAT = 'YYYYMMDD'
USERNAME_INDEX = 0
INSTRUMENT_NAME_INDEX = 1
ALLOTMENT_INDEX = 2
MODIFICATION_INDEX = 3
AUDIT_DATE_INDEX = 4
MODIFIED_TABLE_INDEX = 5
REFERENCE_INDEX = 6
INTERNAL_CODE_INDEX = 7


log_filename = PropertiesUtil.configObject.GetLogFileName()
logging = GTJALogging(log_filename)
logger = logging.GetLogger()


def main(argv):
    # read config
    cfg = PropertiesUtil.configObject.GetConfigProperties()
    cfg_seek_pos_file = cfg['Output'].get('SeekPosFile')
    cfg_sql_file = cfg['Input'].get('SQL')
    cfg_template = cfg['Input'].get('template')
    cfg_allotments = cfg['Input'].get('Allotments')
    cfg_report_file = cfg['Output'].get('ReportFileName')

    # allotments
    raw_allotments = cfg_allotments.split(',')
    stripped_allotments = [s.strip() for s in raw_allotments]
    allotments = ', '.join(f"'{w}'" for w in stripped_allotments)

    today_str = datetime.date.today().strftime('%Y%m%d')
    queryDate = datetime.date.today()
    # queryDate = datetime.date.fromisoformat('2022-09-19')

    # read last position
    instruments_seek_pos = queryDate.strftime('%Y-%m-%d') + ' 00:00:00'
    arbitrage_seek_pos = queryDate.strftime('%Y-%m-%d') + ' 00:00:00'

    if os.path.exists(cfg_seek_pos_file):
        seek_pos_file_read_io = open(cfg_seek_pos_file, 'r')
        instruments_seek_pos_raw = seek_pos_file_read_io.readline()
        arbitrage_seek_pos_raw = seek_pos_file_read_io.readline()
        instruments_seek_pos_raw = instruments_seek_pos_raw.replace('\n', '')
        arbitrage_seek_pos_raw = arbitrage_seek_pos_raw.replace('\n', '')
        seek_pos_file_read_io.close()
        if instruments_seek_pos_raw:
            instruments_seek_pos = instruments_seek_pos_raw
        else:
            logger.warn('incorrect instruments_seek_pos_raw')
        if arbitrage_seek_pos_raw:
            arbitrage_seek_pos = arbitrage_seek_pos_raw
        else:
            logger.warn('incorrect arbitrage_seek_pos_raw')
        logger.info('instruments_seek_pos=%s', instruments_seek_pos)
        logger.info('arbitrage_seek_pos=%s', arbitrage_seek_pos)

    # execute db query
    logger.info('SQL = %s', cfg_sql_file)
    logger.info('Report Date = %s', today_str)
    sophis_db = SophisDBConnection()
    db_conn = cx_Oracle.connect(sophis_db.getUserId(), sophis_db.getPassword(),
                                sophis_db.getHost() + '/' + sophis_db.getDbInstance())
    logger.info(db_conn)
    cursor = db_conn.cursor()
    with open(cfg_sql_file) as file:
        sql = file.read()
    # replace IN (...) with config Allotments
    sql = sql.replace(':allotments', allotments)
    logger.info('SQL query = %s', sql)
    cursor.execute(sql,
                   pos1=instruments_seek_pos,
                   pos2=arbitrage_seek_pos,
                   today=queryDate.strftime('%Y%m%d'))
    db_query_results = cursor.fetchall()
    logger.info('Records = [ %i ]', len(db_query_results))

    # process db results
    records = ''
    if len(db_query_results) > 0:
        arbitrage_found = False
        instruments_found = False
        for record in db_query_results:
            # save the latest modified date of arbitrage and others
            if not arbitrage_found and str(record[MODIFIED_TABLE_INDEX]).lower() == 'arbitrage':
                if str(record[AUDIT_DATE_INDEX]) > arbitrage_seek_pos:
                    arbitrage_seek_pos = str(record[AUDIT_DATE_INDEX])
                    arbitrage_found = True
            elif not instruments_found:
                if str(record[AUDIT_DATE_INDEX]) > instruments_seek_pos:
                    instruments_seek_pos = str(record[AUDIT_DATE_INDEX])
                    instruments_found = True

            records += '<tr>'
            # User
            records += '<td>' + str(record[USERNAME_INDEX]) + '</td>'
            # Audit date
            records += '<td align="right">' + str(record[AUDIT_DATE_INDEX]) + '</td>'
            # Modification
            records += '<td>' + str(record[MODIFICATION_INDEX]) + '</td>'
            # Allotment
            records += '<td>' + str(record[ALLOTMENT_INDEX]) + '</td>'
            # Reference
            records += '<td>' + str(record[REFERENCE_INDEX]) + '</td>'
            # Name
            records += '<td>' + str(record[INSTRUMENT_NAME_INDEX]) + '</td>'
            # Modified table
            records += '<td>' + str(record[MODIFIED_TABLE_INDEX]) + '</td>'
            # Internal code
            records += '<td align="right">' + str(record[INTERNAL_CODE_INDEX]) + '</td>'
            records += '</tr>'
        logger.info(records)

        with open(cfg_template) as file:
            template_content = file.read()
        content = template_content.replace('###Template###', records)
        report_file = cfg_report_file.replace('YYYYMMDD', today_str)
        output_file = open('output/' + report_file, 'w')
        output_file.write(content)
        output_file.close()
        GTJAEmailSender.sendEmail(content, today_str, DATE_FORMAT, alarm=True)
    else:
        logger.info('No record found!')

    logger.info('instruments_seek_pos=%s', instruments_seek_pos)
    logger.info('arbitrage_seek_pos=%s', arbitrage_seek_pos)
    seek_pos_file_write_io = open(cfg_seek_pos_file, 'w+')
    seek_pos_file_write_io.writelines([instruments_seek_pos, '\n', arbitrage_seek_pos])
    seek_pos_file_write_io.close()


if __name__ == '__main__':
    main(sys.argv[1:])
