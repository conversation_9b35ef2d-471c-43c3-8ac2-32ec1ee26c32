from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from email.message import EmailMessage
import cx_Oracle
import datetime
import time
import csv
import os
import smtplib

k = list()
dateFormat = "YYYYMMDD"

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

def sendEmail(content, today_str):
    logger.info("Try to send email")
    sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
    receiptent = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Receiptent')
    subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
    body = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Body')
    smptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
    smptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

    subject = subject.replace(dateFormat, today_str)

    msg = EmailMessage()
    msg['From'] = sender
    msg['To'] = receiptent
    msg['Subject'] = subject
    msg.set_content(content)
    msg.add_alternative(content, subtype='html')
    logger.info("================================================================")
    logger.info(msg)
    if (PropertiesUtil.configObject.IsPRD()):
        with smtplib.SMTP(smptHost, smptPort) as server:
            server.send_message(msg)
    else:
        logger.info("No email sending for DEV or UAT")



today_str = datetime.date.today().strftime('%Y%m%d')
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
template2 = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template2')
reportFileName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFileName')
reportFileName = reportFileName.replace('YYYYMMDD', today_str)
#reportFileName2 = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFileName2')
#reportFileName2 = reportFileName2.replace('YYYYMMDD', today_str)

logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', today_str)
sophisDb = SophisDBConnection()
dbConn2 = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                            sophisDb.getHost() + "/" + sophisDb.getDbInstance())
logger.info(dbConn2)
cursor = dbConn2.cursor()
with open(SQL) as file: sql2 = file.read()
logger.info("SQL query = %s", sql2)
cursor.callproc("dbms_output.enable")
cursor.execute(sql2)
statusVar = cursor.var(cx_Oracle.NUMBER)
lineVar = cursor.var(cx_Oracle.STRING)

while True:
    cursor.callproc("dbms_output.get_line", (lineVar, statusVar))
    if statusVar.getvalue() != 0:
        break
    x = lineVar.getvalue()

    records = ""
    records += "<tr>"
    records += "<td >" + str(x) + "</td>"
    records += "</tr>"
    k.append(records)
    logger.info(k)

if (len(k) > 0):
    with open(template) as file:
        str1 = ""
        for kk in k:
            str1 += kk
        # print(str1)
        templateContent = file.read()
        content = templateContent.replace('###Template###', str1)
        # print(content)
        outputfile = open("output/" + reportFileName, 'w')
        outputfile.write(content)
        outputfile.close()
        sendEmail(content, today_str)

else:
    print('No results')
    with open(template2) as file:
        templateContent2 = file.read()
        str1 = ""
        content2 = templateContent2.replace('###Template###', str1)
        outputfile = open("output/" + reportFileName, 'w')
        outputfile.write(content2)
        outputfile.close()
        sendEmail(content2, today_str)



CSV = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('CSV')
CSVBACK = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('CSVBACK')

today_strr = time.strftime('%Y%m%d %H%M%S')
os.rename(CSV,CSVBACK+'bond_'+today_strr+'.csv')
