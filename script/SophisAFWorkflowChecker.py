## Date         Name                       Detail
##==========================================================================================================================
##20200702      <PERSON>                 Create a health check script to monitor the AF Workflows.
##
##
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import cx_Oracle
import datetime
import smtplib
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage

dateFormat = "YYYYMMDD"

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

def sendEmail(content, today_str, isOK):
    logger.info("Try to send email")
    sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
    receiptent = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Receiptent')
    subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
    body = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Body')
    smptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
    smptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

    subject = subject.replace(dateFormat, today_str)

    if (isOK == True) :
        subject = subject + " - OK"
    else:
        subject = subject + " - FAILED"
    msg = EmailMessage()
    msg['From'] = sender
    msg['To'] = receiptent
    msg['Subject'] = subject
    msg.set_content(content)
    msg.add_alternative(content, subtype='html')
    logger.info("================================================================")
    logger.info(msg)
    if (PropertiesUtil.configObject.IsPRD()):
        with smtplib.SMTP(smptHost, smptPort) as server:
            server.send_message(msg)
    else:
        logger.info("No email sending for DEV or UAT")

today_str = datetime.date.today().strftime('%Y%m%d')
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')

today_str = datetime.date.today().strftime('%Y%m%d')
logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', today_str)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect('sophis_prd_af', 'sophisprdaf', "SophisOSN1.GTJA.COM.HK/riskprd.GTJA.COM.HK")
logger.info(dbConn)
cursor = dbConn.cursor()
with open(SQL) as file: sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql, today=today_str)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))

records = ""
isOK = True
if (len(results) > 0 ):
    for record in results:

        records += "<tr>"
        records += "<td>" + str(record[0]) + "</td>"
        records += "<td>" + str(record[1]) + "</td>"
        records += "<td>" + str(record[2]) + "</td>"
        if (str(record[3]) == 'Succeed'):
            records += "<td id=pass>" + str(record[3]) + "</td>"
        else:
            records += "<td id=fail>" + str(record[3]) + "</td>"
            isOK = False
        records += "</tr>"
    logger.info(records)
    with open(template) as file:
        templateContent = file.read()
    content = templateContent.replace('###Template###', records)
    outputfile = open('output/SophisAFWorkflowChecker.html', 'w')
    outputfile.write(content)
    outputfile.close()
    sendEmail(content, today_str, isOK)
else:
   logger.info("No record found!")
