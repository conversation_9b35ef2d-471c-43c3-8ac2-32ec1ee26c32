import logging
import os
import psutil
import platform
from datetime import datetime
from gj_utils.emailutil import Log<PERSON><PERSON>l, EmailUtil, EmailBuilder
from gj_utils.logutil import LogUtil

# Env configs
SMTP_HOST = "smtp.gtjai.net"
SMTP_FROM = "INST Monitor IT <<EMAIL>>"
TO_ADDR = "<EMAIL>"
# SMTP_HOST = "smtp.gtja.com.uat"
# <AUTHOR> <EMAIL>"
# TO_ADDR = "<EMAIL>"

# Global variables
SYSTEM = "Sophis"
COMPONENT = "Job"
JOB_NAME = "Check DOI report"

log_util = LogUtil(logger_name=__name__, loglevel=logging.DEBUG)
logger = log_util.getLogger()
email_util = EmailUtil(from_addr=SMTP_FROM, host=SMTP_HOST)

process_name = "SophisBatch.exe"
process_cmdline = "GenerateComplianceDOI-FPISDReport.config"
output_path = "D:\Sophis\App\GTJA_Reports\Compliance\out"
output_files = [
    "DOI_ISD_SophisTransaction_YYYYMMDD.csv",
    "DOI_ISD_SophisPosition_YYYYMMDD.csv",
    "DOI_ISD_SophisInstrument_YYYYMMDD.csv",
    "DOI_FP_SophisTransaction_YYYYMMDD.csv",
    "DOI_FP_SophisPosition_YYYYMMDD.csv",
    "DOI_FP_SophisInstrument_YYYYMMDD.csv",
]


def get_pid_by_name_and_cmdline(name: str, cmdline_search: str) -> int:
    pid = None
    logger.info(
        f"Searching pid for process with 'name={name}' and command line contains '{cmdline_search}'"
    )
    for proc in psutil.process_iter(["pid", "name", "cmdline"]):
        proc_info = proc.info
        if proc_info["name"] == name:
            for cmdline_arg in proc_info["cmdline"]:
                if cmdline_search in cmdline_arg:
                    logger.info(f"Process found: {proc_info}")
                    return proc.pid


def check_output() -> bool:
    logger.info(f"Checking output files in: {output_path}")
    result = True
    for file in output_files:
        file_name = file.replace("YYYYMMDD", datetime.today().strftime("%Y%m%d"))
        file_ready = check_file_ready(output_path, file_name)
        if not file_ready:
            result = False

    logger.info(f"All outputs are ready: {result}")
    return result


def check_file_ready(output_path: str, file_name: str) -> bool:
    full_path = os.path.join(output_path, file_name)
    if not os.path.exists(full_path):
        logger.warning(f"{file_name} not exixts.")
        return False

    size = os.path.getsize(full_path)
    if size < 4:
        logger.warning(f"{file_name} exixts but empty.")
        return False

    logger.info(f"{file_name} exixts with size: {(size/1024):.2f} KB.")
    return True


def kill_process_by_pid(pid: int):
    try:
        process = psutil.Process(pid)
        logger.info(f"Process={process}")
        process.terminate()
        process.wait()  # Wait for the process to be terminated
        logger.info(f"Process with PID {pid} has been terminated.")
    except psutil.NoSuchProcess:
        logger.info(f"No process with PID {pid} found.")
    except psutil.AccessDenied:
        logger.error(f"Access denied to terminate process with PID {pid}.")
    except Exception as e:
        logger.error(f"An error occurred: {e}")


def send_notification(log_level: str, subject: str):
    subject = f"[{SYSTEM}][{COMPONENT}][{log_level}] {platform.node()}:{subject}"
    EmailBuilder().setTo(TO_ADDR).setSubject(subject).setBody(
        log_util.getLogString()
    ).buildAndSend(email_util)


def main():
    pid = get_pid_by_name_and_cmdline(process_name, process_cmdline)
    logger.info(f"pid={pid}")
    if pid == None:
        logger.info(f"Done, no running process found.")
        send_notification(LogLevel.INFO, f"{JOB_NAME} - process not running, no action")
        return

    output = check_output()
    if not output:
        logger.error(
            f"Procss found still running but not all outputs ready, sending alert."
        )
        send_notification(
            LogLevel.ERROR,
            f"{JOB_NAME} - process still running and output not ready, please check",
        )

        return

    kill_process_by_pid(pid)
    send_notification(
        LogLevel.WARN,
        f"{JOB_NAME} - process still running but output ready, killed the process",
    )

    return


if __name__ == "__main__":
    main()
