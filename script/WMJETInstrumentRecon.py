from GTJA.SophisDBConnection import SophisDBConnection
from Sophis.JetInstrumentRepository import JetInstrumentRepository
from Sophis.SophisBankHoliday import SophisBankHoliday
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import GTJAEmailSender
import cx_Oracle
import datetime
import os.path
import time

dateFormat = "YYYYMMDD"
SLEEP_TIME = 30

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

businessDate = datetime.date.today().strftime('%Y%m%d')

jetInstrumentFile = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('JetInstrumentFile')
jetInstrumentFile = jetInstrumentFile.replace('YYYYMMDD', businessDate)

while (os.path.isfile(jetInstrumentFile) == False):
    logger.warning("file, %s, not exist, sleep %d sec ", jetInstrumentFile, SLEEP_TIME)
    time.sleep(SLEEP_TIME)

logger.info("Start processing ... ")

jetInstrumentRepository = JetInstrumentRepository(jetInstrumentFile)

logger.info('Jet Instrument file')
logger.debug(jetInstrumentRepository)

SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
holidaySQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('HOLIDAY_SQL')
logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', businessDate)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()
sqlFiles = SQL.split(';')
records = ""
bankHoliday = SophisBankHoliday(dbConn, holidaySQL, businessDate)

containsRecord = False
for sqlFile in sqlFiles:
    logger.info("SQL file = %s", sqlFile)
    with open(sqlFile) as file:
        sql = file.read()
    logger.info("SQL query = %s", sql)
    cursor.execute(sql, today=businessDate)
    results = cursor.fetchall()
    logger.info('Records = [ %i ]', len(results))


    if (len(results) > 0):
        for internalref in results:
            matched = False
            # SophisReference, GTJARef, name, strike, expiry, CallPut, Underlying_GTJARef, conversionRation, currency
            allotment = internalref[0]
            gtjaRef = internalref[2]
            name = internalref[3]
            strike = internalref[4]
            expiry = internalref[5].strftime('%Y%m%d')
            callPut = internalref[6]
            underlyingRef = internalref[7]
            conversionRation = internalref[8]
            fixingDate = internalref[9]
            currency = internalref[10]
            unmatchedReason = ''

            if (gtjaRef[len(gtjaRef) - 3:] == '.HK'):
                hkexSymbol = gtjaRef[0:len(gtjaRef) - 3]
            else:
                hkexSymbol = gtjaRef

            if (underlyingRef[len(underlyingRef) - 3:] == '.HK'):
                underlyingSymbol = underlyingRef[0:len(underlyingRef) - 3]
                logger.info('%s %d', underlyingSymbol, len(underlyingSymbol))
                for pos in range(0, len(underlyingSymbol)):
                    logger.info('pos %d', pos)
                    if (underlyingSymbol[pos] != '0'):
                        underlyingSymbol = underlyingSymbol[pos:]
                        break
                    logger.info('underlying Symbol %s', underlyingSymbol)
            elif (underlyingRef.find('Index') > 0):
                underlyingSymbol = underlyingRef[0:underlyingRef.find('Index')].strip()
            else:
                underlyingSymbol = underlyingRef

            if (hkexSymbol in jetInstrumentRepository.getJetInstrumentRepository()[allotment]):
                logger.info("Instrument exist [%s] ", gtjaRef)
                foundInstrument = jetInstrumentRepository.getJetInstrumentRepository()[allotment][hkexSymbol]
            else:
                logger.error("Instrument not found [%s] ", gtjaRef)
                unmatchedReason = 'Not in JET'
                foundInstrument = None

            expectedFixingDay = bankHoliday.oneDayBefore(currency, internalref[5])
            logger.info("One day before %s %s", expiry, expectedFixingDay.strftime('%Y%m%d'))
            if (foundInstrument != None):
                logger.info('Instrument [%s]', gtjaRef)
                logger.info('strike [%r] [%s] [%s]', (float(foundInstrument.strike) == strike), foundInstrument.strike,
                            strike)
                logger.info('CallPut [%r] [%s] [%s]', (foundInstrument.callPut == callPut), foundInstrument.callPut,
                            callPut)
                logger.info('Currency [%r] [%s] [%s]', (foundInstrument.currency == currency), foundInstrument.currency,
                            currency)
                logger.info('Conversion Ratio [%r] [%s] [%s]',
                            (float(foundInstrument.conversionRatio) == conversionRation),
                            foundInstrument.conversionRatio, conversionRation)
                logger.info('Expiry Date [%r] [%s] [%s]', (foundInstrument.expiryDate == expiry),
                            foundInstrument.expiryDate, expiry)
                logger.info('Underlying [%r] [%s] [%s]', (foundInstrument.underlying == underlyingSymbol),
                            foundInstrument.underlying, underlyingSymbol)
                logger.info('Fixing Date [%r] [%s] [%s]', (expectedFixingDay == fixingDate),
                            expectedFixingDay.strftime('%Y%m%d'), fixingDate.strftime('%Y%m%d'))
                if (float(foundInstrument.strike) != strike):
                    unmatchedReason = 'Incorrect Strike (JET=' + foundInstrument.strike + ')'
                elif (foundInstrument.callPut != callPut):
                    unmatchedReason = 'Incorrect Call Put (JET= ' + foundInstrument.callPut + ')'
                elif (foundInstrument.currency != currency):
                    unmatchedReason = 'Incorrect Currency (JET= ' + foundInstrument.currency + ')'
                elif (float(foundInstrument.conversionRatio) != conversionRation):
                    unmatchedReason = 'Incorrect Conversion Ratio (JET= ' + foundInstrument.conversionRatio + ')'
                elif (foundInstrument.underlying != underlyingSymbol):
                    unmatchedReason = 'Incorrect Underlying (JET= ' + foundInstrument.underlying + ')'
                elif (foundInstrument.expiryDate != expiry and allotment == 'Warrants'):
                    unmatchedReason = 'Incorrect Expiration Date (JET= ' + foundInstrument.expiryDate + ')'
                elif (allotment == 'CBBC'):
                    logger.info('underlying symbol [%s] [%s] ', foundInstrument.underlying, underlyingSymbol)
                    callPrice = internalref[11]
                    logger.info('Call Price [%s] [%s] ', foundInstrument.callPrice, callPrice)
                    if (float(foundInstrument.callPrice) != callPrice):
                        # matched = True
                        unmatchedReason = 'Incorrect Call Price (JET=' + foundInstrument.callPrice + ')'
                        logger.info("%s %s %s %s %s %s %s %s %s %s", allotment, gtjaRef, name, strike, expiry, callPut,
                                    underlyingRef, conversionRation, currency, callPrice)
                    else:
                        matched = True
                else:
                    matched = True
                    # unmatchedReason = 'Not match'
                    logger.info("%s %s %s %s %s %s %s %s %s %s", allotment, gtjaRef, name, strike, expiry, callPut,
                                underlyingRef, conversionRation, currency, unmatchedReason)
                    # logger.info('Instrument not matched %s', foundInstrument)

            if (matched == False):
                containsRecord = True
                records += "<tr>"
                records += "<td>" + str(gtjaRef) + "</td>"
                records += "<td>" + allotment + "</td>"
                records += "<td>" + name + "</td>"
                records += "<td>" + str(strike) + "</td>"
                records += "<td>" + callPut + "</td>"
                records += "<td>" + currency + "</td>"
                records += "<td>" + str(conversionRation) + "</td>"
                records += "<td>" + underlyingRef + "</td>"
                if (allotment == 'CBBC'):
                    records += "<td>" + str(internalref[11]) + "</td>"
                else:
                    records += "<td></td>"
                records += "<td>" + unmatchedReason + "</td>"
                records += "</tr>"

if (containsRecord == True):
	with open(template) as file:
		templateContent = file.read()
		content = templateContent.replace('###Template###', records)
		outputfile = open('output/WMJETInstrumentRecon.html', 'w')
		outputfile.write(content)
		outputfile.close()
		# content, today_str, dateFormat):
		GTJAEmailSender.sendEmail(content, businessDate, dateFormat, alarm=True)