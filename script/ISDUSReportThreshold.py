## Date         Name                       Detail
# This is a python report to check the notional threshold for the US client reporting.
# Email alert is sent when the targeted threshold rate is reached.
# Example:
# Day 1: >=25% & <50% -> Email
# Day 2: >=25% & <50% -> No Email
# Day 3: >=50% & <75% -> Email
# Day 4: >=25% & <50% -> No Email
# Day 5: >=50% & <75% -> Email
# Day 6: >=75% & <80% -> Email
# Day 7: >=25% & <50% -> No Email
# Day 8: >=50% & <75% -> Email
# Day 9: >=75% & <80% -> Email
#
# ==========================================================================================================================
# 20230214      Alvin <PERSON>k                 Initial version
# 20230215      Alvin Mak                 Adopt configurable default FX rate.
# 20230302      Alvin <PERSON>k                 Adjust to cater email trigger logic. Down threshold->No Email. Up threshold->Email
# 20231127      <PERSON>                 Modify as the parent class to support multiple allotments.
# 20240225      Alvin Mak                 Add SQLRecord parameter to count if transactions exist. No record -> notionalSum=0
# 20250404      Alvin Mak                 Fix notionalUSDSum = internalSum[0]
#

from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJAFormatter import GTJAFormatter
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import GTJAEmailSender
import cx_Oracle
import datetime

dateFormat = 'YYYYMMDD'

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

businessDate = datetime.date.today()
businessDate = businessDate.strftime('%Y%m%d')


class Threshold:
    def __init__(self, template, SQLRecord, SQLSum, SQLDetail, outputFilePrefix, defaultFX, thresholdAmt, thresholdPercent, thresholdPercentList, nextThresholdConfigPath, config):
        logger.info('Threshold Parent Class: __init__')
        self.template = template
        self.SQLRecord = SQLRecord
        self.SQLSum = SQLSum
        self.SQLDetail = SQLDetail
        self.outputFilePrefix = outputFilePrefix
        self.defaultFX = defaultFX
        self.thresholdAmt = thresholdAmt
        self.thresholdPercent = thresholdPercent
        self.thresholdPercentList = thresholdPercentList
        self.nextThresholdConfigPath = nextThresholdConfigPath
        self.config = config

    def getNextThreshold(self):
        nextThreshold = self.config.get('Setting', 'NextThreshold')
        return nextThreshold

    def setNextThreshold(self, file_path, section, key, value):
        self.config.set(section, key, value)

        with open(file_path, 'w') as configfile:
            self.config.write(configfile)
            logger.info('Post NextThreshold = %s', value)

    def thresholdReport(self, thresholdRate, dbConn):
        nextThreshold = self.getNextThreshold()
        logger.info('Pre NextThreshold = %s', nextThreshold)

        for percent in self.thresholdPercentList:
            logger.info("thresholdRate = %s, percent = %s", thresholdRate, percent)

            if (thresholdRate > float(percent)) & (float(percent) != 100):
                continue
            # Hit the next level Threshold Rate to issue email
            elif float(thresholdRate) >= float(nextThreshold):
                logger.info("thresholdRate = %s, nextThreshold = %s", thresholdRate, nextThreshold)

                cursorDetail = dbConn.cursor()
                with open(self.SQLDetail) as file:
                    sqlDetail = file.read()
                sqlDetail = sqlDetail.replace('{USDCNH_FX}', self.defaultFX)
                logger.info("SQLDetail query = %s", sqlDetail)
                cursorDetail.execute(sqlDetail)
                resultDetail = cursorDetail.fetchall()
                cursorDetail.close()
                logger.info('Records = [ %i ]', len(resultDetail))

                outputReport = ''
                totalNotionalUSD = 0
                totalNotionalNative = 0
                if len(resultDetail) > 0:
                    outputReport += '<p style=\'color:red;font-size:30px;\'><b>' + 'Threshold Limit Reached ' + str(
                        thresholdRate) + '%!' + '</b></p>'
                    for internalDetail in resultDetail:
                        counterParty = internalDetail[0]
                        tradeDate = internalDetail[1]
                        instrumentRef = internalDetail[2]
                        notionalUSD = internalDetail[3]
                        notionalNative = internalDetail[4]
                        totalNotionalUSD += notionalUSD
                        totalNotionalNative += notionalNative
                        outputReport += '<tr>'
                        outputReport += '<td>' + counterParty + '</td>'
                        outputReport += '<td>' + tradeDate.strftime('%d-%b-%Y') + '</td>'
                        outputReport += '<td>' + instrumentRef + '</td>'
                        #outputReport += '<td align=\'right\'>' + GTJAFormatter.formatAmount(notional, 2) + '</td>'
                        outputReport += '<td align=\'right\'>' + "{:,.{}f}".format(notionalUSD, 2) + '</td>'
                        outputReport += '<td align=\'right\'>' + "{:,.{}f}".format(notionalNative, 2) + '</td>'
                        outputReport += '</tr>'
                else:
                    outputReport += '<tr> <td> No Record </td></tr>'

                if totalNotionalUSD > 0:
                    outputReport += '<tr><td></td><td></td>'
                    outputReport += '<td>Total Volume</td>'
                    outputReport += '<td align=\'right\'>' + "{:,.{}f}".format(totalNotionalUSD, 0) + '</td>'
                    outputReport += '<td align =\'right\'>' + "{:,.{}f}".format(totalNotionalNative, 0) + '</td>'
                    outputReport += '</tr>'

                with open(self.template) as file:
                    templateContent = file.read()
                    content = templateContent.replace('###Template###', outputReport)
                    outputFile = open('output/' + self.outputFilePrefix + '.html', 'w')
                    logger.info(content)
                    outputFile.write(content)
                    outputFile.close()
                    GTJAEmailSender.sendEmail(content, businessDate, dateFormat, True)

                    # Set the Next Threshold Rate the present Threshold percent
                    self.setNextThreshold(self.nextThresholdConfigPath, 'Setting', 'NextThreshold', percent)
                    logger.info("Email Alert Sent")
                    break
            # Adjust the Next Threshold Rate to lower level if the present Threshold percent < nextThreshold
            elif float(percent) < float(nextThreshold):
                self.setNextThreshold(self.nextThresholdConfigPath, 'Setting', 'NextThreshold', percent)
                logger.info("percent = %s, nextThreshold = %s", percent, nextThreshold)
                logger.info("Email Alert Not Sent")
                break
            else:
                logger.info("Checking... Email Alert Not Sent")

    def main(self):
        logger.info('Threshold Parent Class: main()')
        sophisDb = SophisDBConnection()
        dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                                   sophisDb.getHost() + "/" + sophisDb.getDbInstance())

        logger.info(dbConn)
        cursorSum = dbConn.cursor()
        with open(self.SQLRecord) as file:
            sqlRecord = file.read()
        cursorSum.execute(sqlRecord)
        recordCount = cursorSum.fetchall()
        logger.info('SQL Record = [ %i ]', len(recordCount))

        if len(recordCount) > 0:
            for internalCount in recordCount:
                count = internalCount[0]
                logger.info('Transaction Record Count = [ %i ]', count)

        if count == 0:
            notionalUSDSum = 0
        else:
            with open(self.SQLSum) as file:
                sqlSum = file.read()
            sqlSum = sqlSum.replace('{USDCNH_FX}', self.defaultFX)
            logger.info("SQLSum query = %s", sqlSum)
            cursorSum.execute(sqlSum)
            resultSum = cursorSum.fetchall()
            cursorSum.close()
            logger.info('Records = [ %i ]', len(resultSum))

            if len(resultSum) > 0:
                for internalSum in resultSum:
                    notionalUSDSum = internalSum[0]

        # Calculate the Threshold Rate
        thresholdRate = round(notionalUSDSum / float(self.thresholdAmt) * 100, 2)
        logger.info("notionalUSDSum = %s, thresholdAmt= %s", notionalUSDSum, self.thresholdAmt)
        self.thresholdReport(thresholdRate, dbConn)
        dbConn.close()

    if __name__ == "__main__":
        main()
