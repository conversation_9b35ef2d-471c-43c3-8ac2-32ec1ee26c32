## Date         Name                       Detail
# This is a python report to present the total notional amount for US client reporting on daily basis.
#
# ==========================================================================================================================
# 20230306      Alvin Mak                 Initial version
# 20231127      <PERSON> as the parent class to support multiple allotments.
# 20240225      <PERSON> Mak                 Add SQLRecord parameter to count if transactions exist. No record -> notionalSum=0
# 20250404      Alvin Mak                 Enhance to support total notional summary and the counterparty summary.
#

from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJAFormatter import GTJAFormatter
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import GTJAEmailSender
import cx_Oracle
import datetime

dateFormat = 'YYYYMMDD'

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

businessDate = datetime.date.today()
businessDateEmail = businessDate.strftime('%Y%m%d')
businessDateContent = businessDate.strftime('%d-%b-%Y')


class NotionalSummary:
    def __init__(self, template1, template2, SQLRecord, SQLSum1, SQLSum2, outputFilePrefix, defaultFX, thresholdAmt):
        logger.info('NotionalSummary Parent Class: __init__')
        self.template1 = template1
        self.template2 = template2
        self.SQLRecord = SQLRecord
        self.SQLSum1 = SQLSum1
        self.SQLSum2 = SQLSum2
        self.outputFilePrefix = outputFilePrefix
        self.defaultFX = defaultFX
        self.thresholdAmt = thresholdAmt

    def notionalSummary(self, resultSum, dbConn):
        notionalSum = 0
        if len(resultSum) > 0:
            for internalSum in resultSum:
                notionalSum = internalSum[0]

        # Calculate the Threshold Rate
        thresholdRate = round(notionalSum / float(self.thresholdAmt) * 100, 2)
        logger.info("notionalSum = %s, thresholdAmt= %s", notionalSum, self.thresholdAmt)

        outputReport1 = '<p style=\'color:red;font-size:20px;\'><b> Total Notional Summary </b></p>'
        outputReport1 += '<tr>'
        outputReport1 += '<td align=\'center\'>' + businessDateContent + '</td>'
        outputReport1 += '<td align=\'center\'>' + "{:,.{}f}".format(notionalSum, 0) + '</td>'
        outputReport1 += '<td align=\'center\'>' + str(round(thresholdRate, 2)) + '% </td>'
        outputReport1 += '</tr>'
        outputReport1 += '<tr></tr><tr></tr>'

        with open(self.template1) as file:
            templateContent = file.read()
            content1 = templateContent.replace('###Template###', outputReport1)
            logger.info(content1)
        return content1

    def notionalCptySummary(self, resultSum, dbConn):
        totalNotionalUSD = 0
        totalNotionalNative = 0
        thresholdRate = 0
        outputReport2 = '<p style=\'color:red;font-size:20px;\'><b> Counterparty Notional Summary </b></p>'

        if len(resultSum) > 0:
            for internalSum in resultSum:
                counterParty = internalSum[0]
                notionalUSDSum = internalSum[1]
                notionalNativeSum = internalSum[2]
                totalNotionalUSD += notionalUSDSum
                totalNotionalNative += notionalNativeSum
                logger.info("counterParty = %s, notionalUSDSum= %s, notionalNativeSum= %s")

                # Calculate the Counterparty level Threshold Rate
                cptyThresholdRate = round(notionalUSDSum / float(self.thresholdAmt) * 100, 2)
                logger.info("notionalUSDSum = %s, thresholdAmt= %s", notionalUSDSum, self.thresholdAmt)

                # Calculate the overall Threshold Rate
                thresholdRate = round(totalNotionalUSD / float(self.thresholdAmt) * 100, 2)
                logger.info("totalNotionalUSD = %s, thresholdAmt= %s", totalNotionalUSD, self.thresholdAmt)

                outputReport2 += '<tr>'
                outputReport2 += '<td align=\'center\'>' + counterParty + '</td>'
                outputReport2 += '<td align=\'center\'>' + "{:,.{}f}".format(notionalUSDSum, 0) + '</td>'
                outputReport2 += '<td align=\'center\'>' + str(round(cptyThresholdRate, 2)) + '% </td>'
                outputReport2 += '</tr>'

        outputReport2 += '<tr>'
        outputReport2 += '<td align=\'right\'>Total</td>'
        outputReport2 += '<td align=\'center\'>' + "{:,.{}f}".format(totalNotionalUSD, 0) + '</td>'
        outputReport2 += '<td align =\'center\'>' + str(round(thresholdRate, 2)) + '% </td>'
        outputReport2 += '</tr>'

        with open(self.template2) as file:
            templateContent = file.read()
            content2 = templateContent.replace('###Template###', outputReport2)
            logger.info(content2)
        return content2

    def process(self, dbConn):
        try:
            cursorSum = dbConn.cursor()
            with open(self.SQLSum1, 'r') as SQLFile1:
                InputSQL1 = SQLFile1.read()
                InputSQL1 = InputSQL1.replace('{USDCNH_FX}', self.defaultFX)
                logger.info('InputSQL1 = %s', InputSQL1)
                cursorSum.execute(InputSQL1)
                resultSum = cursorSum.fetchall()
                logger.info('Records = [ %i ]', len(resultSum))
                SQLFile1.close()

                content1 = self.notionalSummary(resultSum, dbConn)

            with open(self.SQLSum2, 'r') as SQLFile2:
                InputSQL2 = SQLFile2.read()
                InputSQL2 = InputSQL2.replace('{USDCNH_FX}', self.defaultFX)
                logger.info('InputSQL12= %s', InputSQL2)
                cursorSum.execute(InputSQL2)
                resultSum = cursorSum.fetchall()
                logger.info('Records = [ %i ]', len(resultSum))
                SQLFile2.close()

                content2 = self.notionalCptySummary(resultSum, dbConn)

                # Prepare the final output summary tables
                outputFile = open('output/' + self.outputFilePrefix + '.html', 'w')
                outputFile.write(content1 + content2)

                cursorSum.close()
                outputFile.close()

                GTJAEmailSender.sendEmail(content1 + content2, businessDateEmail, dateFormat, True)
        except Exception as e:
            logger.info('%s', str(e))

    def main(self):
        logger.info('NotionalSummary Parent Class: main()')
        sophisDb = SophisDBConnection()
        dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                                   sophisDb.getHost() + "/" + sophisDb.getDbInstance())

        logger.info(dbConn)
        self.process(dbConn)
        dbConn.close()

    if __name__ == "__main__":
        main()

