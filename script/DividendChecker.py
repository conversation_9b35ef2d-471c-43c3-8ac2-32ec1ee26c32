from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import cx_Oracle
import datetime
import smtplib
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage

dateFormat = "YYYYMMDD"

def sendEmail(content, today_str):
    logger.info("Try to send email")
    sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
    receiptent = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Receiptent')
    subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
    smptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
    smptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

    subject = subject.replace(dateFormat, today_str)

    subject = subject + " - ALERT"
    msg = EmailMessage()
    msg['From'] = sender
    msg['To'] = receiptent
    msg['Subject'] = subject
    msg.set_content(content)
    msg.add_alternative(content, subtype='html')
    logger.info("================================================================")
    logger.info(msg)
    with smtplib.SMTP(smptHost, smptPort) as server:
        server.send_message(msg)

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')

today_str = datetime.date.today().strftime('%Y%m%d')
logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', today_str)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(), sophisDb.getHost()+"/"+sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()
with open(SQL) as file: sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))

records = ""

if ( len(results) > 0 ):
    for internalref in results:
        records += "<tr>"
        records += "<td>" + internalref[1] + "</td>"
        records += "<td>" + internalref[0] + "</td>"
        records += "<td>" + internalref[2].strftime('%Y-%m-%d') + "</td>"
        records += "<td>" + internalref[3].strftime('%Y-%m-%d') + "</td>"
        records += "</tr>"
    logger.info(records)
    with open(template) as file: templateContent = file.read()
    content = templateContent.replace('###Template###', records)
    outputfile = open('output/DividendChecker.html','w')
    outputfile.write(content)
    outputfile.close()
    sendEmail(content, today_str)

	