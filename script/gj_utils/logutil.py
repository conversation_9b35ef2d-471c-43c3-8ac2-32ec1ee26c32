import logging
from typing import Optional
from io import StringIO

default_format = "%(asctime)s:%(levelname)s:%(name)s:%(message)s"
logging.basicConfig()

# TODO: add file handler and housekeeping


class LogUtil:
    logger: logging.Logger
    stream: StringIO

    def __init__(
        self,
        logger_name: str,
        loglevel: Optional[int] = logging.INFO,
        format: Optional[str] = default_format,
    ):
        self.stream = StringIO()
        steam_handler = logging.StreamHandler(self.stream)
        formatter = logging.Formatter(format)
        steam_handler.setFormatter(formatter)

        logger = logging.getLogger(logger_name)
        logger.setLevel(loglevel)
        logger.addHandler(steam_handler)

        self.logger = logger

    def getLogger(self) -> logging.Logger:
        return self.logger

    def getLogString(self, clearBuffer: bool = True) -> str:
        log = self.stream.getvalue().replace("\n", '<br>')
        if clearBuffer:
            self.stream.seek(0)
            self.stream.truncate()
        return log

    def __del__(self):
        self.stream.close()
