import smtplib
from enum import Enum
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from typing_extensions import Self


class LogLevel:
    CRITICAL = "CRITICAL"
    ERROR = "ERROR"
    WARN = "WARN"
    INFO = "INFO"


class EmailUtil:
    smtp_client: smtplib.SMTP
    from_addr: str

    def __init__(self, from_addr, host, port: int = 25):
        host = host
        port = port
        self.smtp_client = smtplib.SMTP(host, port)
        self.from_addr = from_addr

    def send(self, to_addrs: str, msg: str):
        self.smtp_client.sendmail(self.from_addr, to_addrs, msg)


class EmailBuilder:
    to_addresses: str
    cc_addresses: str
    bcc_addresses: str
    subject: str
    body: str

    def __init__(self):
        self.to_addresses = None
        self.cc_addresses = None
        self.bcc_addresses = None
        self.subject = None
        self.body = None

    def setTo(self, to_addresses: str) -> Self:
        self.to_addresses = to_addresses
        return self

    def setCc(self, cc_addresses: str) -> Self:
        self.cc_addresses = cc_addresses
        return self

    def setBcc(self, bcc_addresses: str) -> Self:
        self.bcc_addresses = bcc_addresses
        return self

    def setSubject(self, subject: str) -> Self:
        self.subject = subject
        return self

    def setBody(self, body: str) -> Self:
        self.body = body
        return self

    def buildAndSend(self, emailUtil: EmailUtil):
        msg = MIMEMultipart("alternative")
        msg["From"] = emailUtil.from_addr
        msg["To"] = self.to_addresses
        to_addrs = self.to_addresses.split(",")
        if self.cc_addresses != None:
            msg["CC"] = self.cc_addresses
            to_addrs.append(self.cc_addresses.split(","))
        if self.bcc_addresses != None:
            msg["Bcc"] = self.bcc_addresses
            to_addrs.append(self.bcc_addresses.split(","))
        msg["Subject"] = self.subject

        body = MIMEText(self.body, "html")
        msg.attach(body)

        return emailUtil.send(to_addrs, msg.as_string())
