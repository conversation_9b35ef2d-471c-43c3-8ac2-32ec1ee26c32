from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import StringUtil

import cx_Oracle
import datetime
import sys
import os

""" program start """
logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
try:
    """ set today """
    today = datetime.date.today()


    """ read parameters """
    BucketName = PropertiesUtil.configObject.GetConfigProperties()['Scripts'].get('BucketName')
    OutputFileName = PropertiesUtil.configObject.GetConfigProperties()['Scripts'].get('OutputFileName')
    BusinessDate = PropertiesUtil.configObject.GetConfigProperties()['Others'].get('BusinessDate')

    Perimeter = PropertiesUtil.configObject.GetConfigProperties()['FXParam'].get('Perimeter')
    CalculationMode = PropertiesUtil.configObject.GetConfigProperties()['FXParam'].get('CalculationMode')
    DetailedResults = PropertiesUtil.configObject.GetConfigProperties()['FXParam'].get('DetailedResults')
    OverVolatility = PropertiesUtil.configObject.GetConfigProperties()['FXParam'].get('OverVolatility')
    BumpMarketPlot = PropertiesUtil.configObject.GetConfigProperties()['FXParam'].get('BumpMarketPlot')
    CalculateVolga = PropertiesUtil.configObject.GetConfigProperties()['FXParam'].get('CalculateVolga')
    CalculateReserve = PropertiesUtil.configObject.GetConfigProperties()['FXParam'].get('CalculateReserve')
    ResultOutputfile = PropertiesUtil.configObject.GetConfigProperties()['FXParam'].get('ResultOutputfile')

    logger.info('BucketName = %s', Perimeter)
    logger.info('OutputFileName = %s', OutputFileName)
    logger.info('BusinessDate = %s', BusinessDate)

    logger.info('Perimeter = %s', Perimeter)
    logger.info('CalculationMode = %s', CalculationMode)
    logger.info('DetailedResults = %s', DetailedResults)
    logger.info('OverVolatility = %s', OverVolatility)
    logger.info('BumpMarketPlot = %s', BumpMarketPlot)
    logger.info('CalculateVolga = %s', CalculateVolga)
    logger.info('CalculateReserve = %s', CalculateReserve)
    logger.info('ResultOutputfile = %s', ResultOutputfile)

    """ format date """
    if BusinessDate != '':
        today = datetime.datetime.strptime(BusinessDate, '%Y%m%d')
        logger.info('Set today = %s', BusinessDate)

    """ param """
    sql_get_strike = 'select strike,strikepct from grid_models a left join grid_strikes b on a.IDENT = b.GRIDMODEL where name = :BucketName order by a.ident, b.strike asc'
    sql_get_maturity = 'select case unite when 0 then TO_CHAR(num_to_date(maturity),\'YYYY-MM-DD\') when 1 then TO_CHAR(maturity) || \'d\' when 2 then TO_CHAR(maturity) || \'m\' when 3 then TO_CHAR(maturity) || \'y\' else TO_CHAR(maturity) end as maturity, unite from grid_models a left join grid_maturities b on a.ident = b.gridmodel where name = :BucketName order by a.ident, b.maturity asc'

    logger.info('sql_get_strike = %s', sql_get_strike)
    logger.info('sql_get_maturity = %s', sql_get_maturity)

    """ write parameter"""
    with open(OutputFileName, 'w+') as Resultfile:
        Resultfile.write('<scenario:scenario xmlns:scenario="http://www.sophis.net/scenario" xmlns:common="http://sophis.net/sophis/common" xmlns:folio="http://www.sophis.net/folio" xmlns:instrument="http://www.sophis.net/Instrument" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">')
        Resultfile.write('\n')
        Resultfile.write(Perimeter)
        Resultfile.write('\n')
        Resultfile.close()
    with open(OutputFileName, "a") as Resultfile:
        Resultfile.write('<scenario:parameters xsi:type="scenario:FXVolMatrixMaturity">')
        Resultfile.write('\n')
        Resultfile.write('<scenario:calculationMode>'+CalculationMode+'</scenario:calculationMode>')
        Resultfile.write('\n')
        Resultfile.write('<scenario:detailedResults>'+DetailedResults+'</scenario:detailedResults>')
        Resultfile.write('\n')
        Resultfile.write('<scenario:overVolatility>'+OverVolatility+'</scenario:overVolatility>')
        Resultfile.write('\n')
        Resultfile.close()

    """ connection to the database """
    sophisDb = SophisDBConnection()
    with cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance()) as dbConn:
        logger.info(dbConn)

        with dbConn.cursor() as cursor:
            """ read maturity date"""
            cursor.execute(sql_get_strike, BucketName=BucketName)
            logger.info('Get strike %s' % BucketName)
            results = cursor.fetchall()
            """ write strike result to a file"""
            if cursor.rowcount > 0:
                logger.info('Number of strike %s' % cursor.rowcount)
                with open(OutputFileName, "a") as Resultfile:

                    if results[0][1] == 0:
                        Resultfile.write('<scenario:strikeType>InPercent</scenario:strikeType>')
                    else:
                        Resultfile.write('<scenario:strikeType>InPoints</scenario:strikeType>')

                    Resultfile.write('\n')

                    for line in results:
                        logger.info('strike %d' % line[0])
                        Resultfile.write('<scenario:strike>'+str(line[0])+'</scenario:strike>')
                        Resultfile.write('\n')

                    Resultfile.close()

            logger.info('Get maturity %s' % BucketName)
            cursor.execute(sql_get_maturity, BucketName=BucketName)
            results = cursor.fetchall()
            """ write maturity result to a file"""
            if cursor.rowcount > 0:
                with open(OutputFileName, "a") as Resultfile:
                    Resultfile.write('<scenario:absoluteDateForOutput>true</scenario:absoluteDateForOutput>')
                    Resultfile.write('\n')

                    for line in results:
                        logger.info('Maturity %s' % line[0])
                        if line[1] == 0:
                            Resultfile.write('<scenario:maturity><scenario:absoluteDate>' + str(line[0]) + '</scenario:absoluteDate></scenario:maturity>')
                        else:
                            Resultfile.write('<scenario:maturity><scenario:relativeDate>' + str(line[0]) + '</scenario:relativeDate></scenario:maturity>')
                        Resultfile.write('\n')
                    Resultfile.close()

    with open(OutputFileName, "a") as Resultfile:
        Resultfile.write('<scenario:tenor><scenario:absoluteDate>'+today.strftime("%Y-%m-%d")+'</scenario:absoluteDate></scenario:tenor>')
        Resultfile.write('\n')
        Resultfile.write('<scenario:bumpMarketPlot>'+BumpMarketPlot+'</scenario:bumpMarketPlot>')
        Resultfile.write('\n')
        Resultfile.write('<scenario:calculateVolga>'+CalculateVolga+'</scenario:calculateVolga>')
        Resultfile.write('\n')
        Resultfile.write('<scenario:calculateReserve>'+CalculateReserve+'</scenario:calculateReserve>')
        Resultfile.write('\n')
        Resultfile.write('</scenario:parameters>')
        Resultfile.write('\n')

        if ResultOutputfile!='':
            Resultfile.write('<scenario:outputfile>'+ResultOutputfile+'</scenario:outputfile>')
            Resultfile.write('\n')
        Resultfile.write('</scenario:scenario>')
        Resultfile.write('\n')
        Resultfile.close()

except Exception as e:
    logger.info('%s', str(e))
