SELECT trades.refcon AS tradeId,
  --trades.sicovam,
  SophisFolio.name                                                               AS portfolio,
  instrument.reference                                                           AS instrument_reference,
  trades.dateneg                                                                 AS trade_date,
  trades.dateval                                                                 AS value_date,
  entity.name                                                                    AS entity,
  counterparty.name                                                              AS counterparty,
  DEVISE_TO_STR(instrument.devisectt)                                            AS CCY,
  ROUND(Equity.notional*trades.quantite, 6)                                      AS Nominal ,
  trades.INITIAL_MARGIN                                                          AS IM_Factor,
  ROUND(Equity.notional * trades.initial_margin / 100 * (trades.quantite*-1), 6) AS IM_Amount
FROM
  (SELECT t.opcvm,
    t.sicovam,
    SUM(t.quantite) AS quantity
  FROM JOIN_POSITION_HISTOMVTS t
  LEFT JOIN titres instrument
  ON t.sicovam=instrument.sicovam
  LEFT JOIN affectation allotment
  ON instrument.affectation = allotment.ident
  LEFT JOIN BUSINESS_EVENTS be
  ON t.TYPE = be.ID
  LEFT JOIN bo_kernel_status status
  ON t.backoffice          = status.ID
  WHERE allotment.libelle           IN ('Equity Swap')
  AND t.dateneg            < sysdate -1
  AND be.NAME                       IN ('Purchase/Sale', 'Unwind', 'Expiry')
  AND status.NAME                   IN ('MO Accepted', 'BO Confirmed')
  GROUP BY t.opcvm,
    t.sicovam
  ) gtja
LEFT JOIN titres instrument
ON gtja.sicovam=instrument.sicovam
LEFT JOIN folio SophisFolio
ON SophisFolio.ident=gtja.opcvm,
  join_position_histomvts trades
LEFT JOIN BUSINESS_EVENTS be
ON trades.TYPE = be.ID
LEFT JOIN tiers entity
ON trades.ENTITE=entity.ident
LEFT JOIN tiers counterparty
ON trades.contrepartie = counterparty.ident
LEFT JOIN bo_kernel_status status
ON trades.backoffice = status.ID,
  (SELECT adj.code,
    ROUND(SUM(b.quantity*b.fixing/b.fx_fixing),6) AS notional
  FROM TRS_BASKET b
  LEFT JOIN titres titre
  ON titre.sicovam=b.sicovam,
    TRS_BASKET_ADJUSTMENT adj
  WHERE b.code  =adj.historicid
  AND adj.NUMERO=0
  AND titre.libelle NOT LIKE 'Equity Swap%'
  GROUP BY adj.code
  ) Equity
WHERE gtja.sicovam =trades.sicovam
AND Equity.code    = gtja.sicovam
AND trades.dateneg < sysdate -1
AND gtja.opcvm     = trades.opcvm
AND gtja.quantity != 0
AND status.NAME   IN ('MO Accepted', 'BO Confirmed')
AND be.NAME       IN ('Purchase/Sale')