select t.REFCON AS Trade_ID,
TO_CHAR(t.DATENEG, 'DD-MON-YY') AS Trade_Date,
(SELECT NAME FROM BUSINESS_EVENTS where t.TYPE = ID) AS Business_Event,
s.name,
(SELECT NAME FROM FOLIO where t.OPCVM = IDENT) AS Portfolio,
instrument.LIBELLE,
t.QUANTITE AS Quantity,
t.COURS AS Gross_Price,
t.MONTANT AS Net_Amount,
(SELECT NAME FROM RISKUSERS where t.OPERATEUR = IDENT) AS Operator
From JOIN_POSITION_HISTOMVTS t left join titres instrument on t.sicovam=instrument.sicovam left join BO_KERNEL_STATUS s
on t.BACKOFFICE=s.ID
WHERE t.DATECOMPTABLE = TRUNC(sysdate)
AND t.CREATION=2
AND t.OPERATEUR=2880