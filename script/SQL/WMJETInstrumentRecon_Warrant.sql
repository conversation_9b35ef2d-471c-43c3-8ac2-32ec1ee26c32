SELECT 'Warrant', REF.sophis_ident as <PERSON><PERSON><PERSON><PERSON>eference,
  ref.VALUE as GT<PERSON><PERSON><PERSON>,
  t.libelle as name,
  t.PRIXEXER as strike,
  t.finper as expiry,
  CASE t.typepro
    WHEN 1
    THEN 'C'
    ELSE 'P'
  END as CallPut,
  underlyer.VALUE as Underlying_GTJARef,
  t.PROPORTION as conversionRation,
  max(c.datefin) as last_fixing_date,
  devise_to_str(t.DEVISEAC) as currency
FROM EXTRNL_REFERENCES_INSTRUMENTS ref
JOIN TITRES t
ON ref.SOPHIS_IDENT=t.sicovam
AND t.affectation IN
  (SELECT ident FROM AFFECTATION WHERE Libelle IN ('Warrants')
  )
LEFT JOIN EXTRNL_REFERENCES_INSTRUMENTS underlyer
ON t.codesj   = underlyer.SOPHIS_IDENT
and underlyer.REF_IDENT=3, clause c
WHERE t.sicovam = c.sicovam
and finper >= to_date(:today, 'YYYYMMDD')
group by REF.sophis_ident,
  ref.<PERSON>L<PERSON>,
  t.libelle,
  t.<PERSON>IXEXER,
  t.finper,
  t.typepro,
  underlyer.VALUE,
  t.PROPORTION,
  t.DEVISEAC