select t.refcon, b.version, status.name, t.opcvm, f.name, instrument.LIBELLE, TO_CHAR(b.datemodif, 'YYYYMMDD HH24:MI:SS'),ru.name as sophis_user
from JOIN_POSITION_HISTOMVTS t
left join folio f on t.opcvm=f.ident left join titres instrument
on t.sicovam = instrument.sicovam, audit_mvt b
left join riskusers ru on b.userid=ru.ident
left join BO_KERNEL_STATUS status
on b.BACKOFFICE=status.id
where t.refcon = b.refcon
and b.datemodif > (to_timestamp(:auditTime, 'YYYYMMDDHH24MISS'))
and RU.name in ('nlo', 'rlee', 'mtong')
order by t.refcon, b.version desc