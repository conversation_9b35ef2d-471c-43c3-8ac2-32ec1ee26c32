SELECT
TMP.CP_FULL_NAME,
NVL(SUM(TMP.USD_NOTIONAL),0),
NVL(SUM(TMP.UNDERLYING_NOTIONAL),0)
FROM (
SELECT
RPT.CP_FULL_NAME,
ROUND(RPT.UNDERLYING_NOTIONAL /
(CASE WHEN RPT.UNDERLYING_FX > 1 THEN RPT.UNDERLYING_FX
     WHEN RPT.UNDERLYING_FX = 1 THEN NVL(FXRATE.FX, {USDCNH_FX})
 END), 2) AS USD_NOTIONAL,
ROUND(RPT.UNDERLYING_NOTIONAL, 2) AS UNDERLYING_NOTIONAL
FROM GTJA_SSD_US_TRADE_REPORT RPT
LEFT JOIN
(select sicovam, JOUR, D FX from Historique) FXRATE ON
RPT.TRADE_DATE = FXRATE.JOUR
JOIN DUAL ON
FXRATE.sicovam =  str_to_devise(RPT.INSTRUMENT_CCY)
WHERE RPT.BACKOFFICE_STATUS IN (369,545) AND
RPT.TRADE_DATE > add_months(SYSDATE,-12) AND
RPT.ALLOTMENT_NAME = 'Equity Swap' AND
RPT.MOVEMENT_BUSINESS_EVENT_NAME = 'Purchase/Sale'
) TMP
GROUP BY TMP.CP_FULL_NAME
ORDER BY TMP.CP_FULL_NAME

