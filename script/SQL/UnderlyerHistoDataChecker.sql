SELECT instrument.sicovam,
  InstrumentReference.VALUE,
  instrument.LIBELLE,
  h.jour
FROM historique h
LEFT JOIN titres instrument
ON h.sicovam=instrument.sicovam
LEFT JOIN EXTRNL_REFERENCES_INSTRUMENTS instrumentReference
ON instrument.sicovam = instrumentReference.sophis_ident
AND InstrumentReference.ref_ident = 3, Affectation a
WHERE
InstrumentReference.VALUE NOT IN ('06688.HK') AND
instrument.AFFECTATION = a.ident
AND (a.LIBELLE in ('Shares', 'ETF', 'REIT'))
AND h.jour IN (SELECT DISTINCT jour FROM historique WHERE jour > CURRENT_DATE - 10)
AND h.D IS NULL order by SICOVAM, JOUR