select 'insert into grid_maturities(gridmodel,maturity,unite) select ' || gridmodel || ',' || maturity || ',' || unite || ' from dual where not exists ( select * from grid_maturities where (gridmodel ='|| gridmodel ||' and maturity =' || maturity || ' and unite ='  || unite ||  '))'
from grid_maturities
where
gridmodel in
(
    select
        ident
    from grid_models
    where name = :BucketName
) and
num_to_date(maturity) <= trim(:business_date)