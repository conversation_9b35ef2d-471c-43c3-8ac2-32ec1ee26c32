SELECT Book, Intruement_Name, Maturity, Allotment, No_of_Securities, Counterparty_Name FROM (
(SELECT
	folio.name AS Book,
	titres.libelle AS Intruement_Name,
	TO_CHAR(titres.echeance,'YYYY-MM-DD') AS Maturity,
	affectation.libelle AS Allotment,
  SUM(mvts.quantite) AS No_of_Securities,
	tiers.NAME AS Counterparty_Name
FROM JOIN_POSITION_histomvts mvts
JOIN business_events be ON mvts.type = be.id and be.name IN ('Purchase/Sale','Unwind','Expiry','Instr. modif','Redemption Package','Opt Cash Settled')
JOIN bo_kernel_status ks ON mvts.backoffice = ks.id AND ks.name NOT LIKE '%Rejected' AND ks.name NOT LIKE '%Cancelled'
JOIN tiers ON mvts.contrepartie = tiers.ident
JOIN folio ON mvts.opcvm = folio.ident
JOIN titres ON mvts.sicovam = titres.sicovam
JOIN affectation ON titres.affectation = affectation.ident
AND  affectation.libelle IN ('Bond Option', 'Digital Options')
AND  mvts.opcvm in (select ident from folio connect by mgr = prior ident start with ident=14833)
AND  mvts.dateneg<=(TO_DATE('01/01/1904', 'DD/MM/YYYY') + GetCurrentDateForExtraction() + 0)
AND  titres.echeance != TO_DATE('01/01/1904', 'DD/MM/YYYY')
AND  titres.echeance <= SYSDATE
GROUP BY
	folio.name,
	titres.libelle,
	titres.echeance,
	affectation.libelle,
	tiers.name
HAVING SUM(mvts.quantite) != 0
)
UNION
(SELECT
	folio.name AS Book,
	titres.libelle AS Intruement_Name,
	TO_CHAR(titres.finper,'YYYY-MM-DD') AS Maturity,
	affectation.libelle AS Allotment,
  SUM(mvts.quantite) AS No_of_Securities,
	tiers.NAME AS Counterparty_Name
FROM JOIN_POSITION_histomvts mvts
JOIN business_events be ON mvts.type = be.id and be.name IN ('Purchase/Sale','Unwind','Expiry','Instr. modif','Redemption Package','Opt Cash Settled')
JOIN bo_kernel_status ks ON mvts.backoffice = ks.id AND ks.name NOT LIKE '%Rejected' AND ks.name NOT LIKE '%Cancelled'
JOIN tiers ON mvts.contrepartie = tiers.ident
JOIN folio ON mvts.opcvm = folio.ident
JOIN titres ON mvts.sicovam = titres.sicovam
JOIN affectation ON titres.affectation = affectation.ident
AND  affectation.libelle IN ('Bond Option', 'Digital Options')
AND  mvts.opcvm in (select ident from folio connect by mgr = prior ident start with ident=14833)
AND  mvts.dateneg<=(TO_DATE('01/01/1904', 'DD/MM/YYYY') + GetCurrentDateForExtraction() + 0)
AND  titres.finper is not NULL
AND  titres.finper <= SYSDATE
GROUP BY
	folio.name,
	titres.libelle,
	titres.finper,
	affectation.libelle,
	tiers.name
HAVING SUM(mvts.quantite) != 0)
)
ORDER BY Book, Intruement_Name