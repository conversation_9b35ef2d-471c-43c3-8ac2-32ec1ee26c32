select REFCON AS Trade_ID,
TO_CHAR(DATENEG, 'DD-MON-YY') AS Trade_Date,
TO_CHAR(DATEMODIF, 'DD-MON-YY HH:MI:SS AM') AS Modified_Time,
(SELECT NAME FROM RISKUSERS where OPERATEUR = IDENT) AS Operator,
(SELECT NAME FROM RISKUSERS where USERID = IDENT) AS USER_ID,
(SELECT NAME FROM BUSINESS_EVENTS where TYPE = ID) AS Business_Event,
(SELECT NAME FROM BO_KERNEL_STATUS where BACKOFFICE = ID) AS Status,
(SELECT REFERENCE FROM TITRES where JOIN_AUDITPOS_AUDIT_MVT.SICOVAM = TITRES.SICOVAM) AS Instrument_Reference,
(SELECT LIBELLE FROM TITRES where JOIN_AUDITPOS_AUDIT_MVT.SICOVAM = TITRES.SICOVAM) AS Name,
(SELECT NAME FROM FOLIO where OPCVM = IDENT) AS Portfolio,
QUANTITE AS Quantity,
COURS AS Gross_Price,
MONTANT AS Net_Amount
From JOIN_AUDITPOS_AUDIT_MVT 
WHERE TRUNC(DATEMODIF) = TRUNC(sysdate)
and OPCVM in (select ident from folio start with ident in ('13854') connect by mgr = prior ident) 
and USERID in (select IDENT from RISKUSERS where NAME in ('awang','ctse','dleung','jzhou','keyuen','kso','mtong','nlo','rlee','rzhou','davwong','sylvia.sum','ejiang')) 
and BACKOFFICE <> '545' 
and NOT (OPERATEUR = '2380' AND USERID = '1')
and REFCON NOT IN (SELECT REFCON FROM JOIN_AUDITPOS_AUDIT_MVT OPERATEUR WHERE OPERATEUR = 2380 AND BACKOFFICE  = 369 AND REFCON IN (SELECT REFCON FROM JOIN_AUDITPOS_AUDIT_MVT
    WHERE OPERATEUR = 2380
    AND VERSION     = 1
    AND BACKOFFICE  = 543
    ))
and (select count(*)
From JOIN_AUDITPOS_AUDIT_MVT 
WHERE TRUNC(DATEMODIF) = TRUNC(sysdate)
and OPCVM in (select ident from folio start with ident in ('13854') connect by mgr = prior ident) 
and USERID in (select IDENT from RISKUSERS where NAME in ('awang','ctse','dleung','jzhou','keyuen','kso','mtong','nlo','rlee','rzhou','davwong','sylvia.sum','ejiang')) 
and BACKOFFICE <> '545' 
and NOT (OPERATEUR = '2380' AND USERID = '1')
and REFCON NOT IN (SELECT REFCON FROM JOIN_AUDITPOS_AUDIT_MVT OPERATEUR WHERE OPERATEUR = 2380 AND BACKOFFICE  = 369 AND REFCON IN (SELECT REFCON FROM JOIN_AUDITPOS_AUDIT_MVT
    WHERE OPERATEUR = 2380
    AND VERSION     = 1
    AND BACKOFFICE  = 543
    ))) > :value order by DATEMODIF DESC