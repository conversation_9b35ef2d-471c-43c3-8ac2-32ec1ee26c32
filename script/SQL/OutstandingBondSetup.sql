DECLARE
 TYPE arrayofnumbers IS TABLE OF sector_instrument_association.SICOVAM%TYPE INDEX BY PLS_INTEGER;
 v_bondIds arrayofnumbers;
 v_ref VARCHAR2(24);
BEGIN
SELECT SICOVAM BULK COLLECT INTO v_bondIds FROM sector_instrument_association WHERE sector IN (SELECT ID from sectors where parent in (SELECT ID FROM sectors where name='Coupon Type') and name='VARIABLE') AND sicovam in (select sicovam from titres where type = 'O');

   FOR i IN 0..v_bondIds.COUNT LOOP 
   begin
    SELECT REFERENCE INTO v_ref FROM titres WHERE SICOVAM=v_bondIds(i) AND FIXE1=0 AND CROIDIV=0 AND BASE1=0 AND REFERENCE NOT IN('XS2349107875','XS2328152058','XS2359197378','163188.SH');
		
    EXCEPTION
      WHEN NO_DATA_FOUND THEN
        v_ref := NULL;
    end;
		IF (v_ref is not null) THEN
			dbms_output.put_line(v_ref); 
		END IF;
		
    END LOOP;
END;