select master.sicovam,
master.reference,
master.libelle,
master.allotment,
data.quantite,
instrument.sicovam as composition,
instrument.libelle as composition_name,
instrument.reference as composition_reference,
DEVISE_TO_STR(instrument.DEVISECTT),
GTJA_DAYCOUNTTOSTR(instrument.BASIS_AC),
instrument.fixe1,
case when interestRate.libelle is null then 'FIXED' else interestRate.libelle end,
case when interestRate.libelle is null then GTJA_FREQUENCYTOSTR(instrument.FREQ_COUPON) else GTJA_FREQUENCYTOSTR(instrument.DUREE1) end,
instrument.CROIDIV
from
(select t.sicovam, t.libelle, t.type, t.reference, a.libelle as allotment from titres t, AFFECTATION a where t.type='N'
and t.AFFECTATION = a.ident
and a.LIBELLE in ('LNOTE - BONDS', 'PNOTE', 'TRS Bonds', 'LNOTE - EQUITY', 'TRS Bonds - Basket')) master left join paniercompo link on master.sicovam = link.sicovam left join
PANIERVALIDITY link_validity on link.IDCOMPOSITION = link_validity.IDCOMPOSITION left join panierdata data on link_validity.IDCOMPOSITIONVALIDITY = data.IDCOMPOSITIONVALIDITY,
titres instrument left join titres interestRate on instrument.BASE1 is not null and instrument.BASE1 = interestRate.sicovam
where
data.SICOPANIER=instrument.sicovam
order by master.sicovam, composition