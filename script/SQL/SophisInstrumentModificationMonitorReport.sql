SELECT DISTINCT
  RU.NAME AS USERNAME,
  INSTRUMENT.LIBELLE AS INSTRUMENT_NAME,
  ALLOTMENT.LIBELLE AS ALLOTMENT_NAME,
  DECODE(MODIF, 1, 'Insert', 2, 'Update', 3, 'Delete', 10, 'Scheduling') AS ACTION,
  TO_CHAR(NUM_TO_DATE(DATE_VALIDITE), 'YYYY-MM-DD HH24:MI:SS') AS MODIFIED_TIMESTAMP,
  NOM_TABLE,
  INSTRUMENT.REFERENCE,
  ADT.SICOVAM
FROM (
  SELECT
    SICOVAM,
    DATE_VALIDITE,
    USERIDENT,
    MODIF,
    NOM_TABLE,
    VALUE_DATE
  FROM INFOS_HISTO
  WHERE ((TYPE_TABLE  <> 16
  OR (TYPE_TABLE       = 16
  AND LOWER(NOM_TABLE) = LOWER('Historique'))))
  AND DATE_VALIDITE >= DATE_TO_NUM(TO_DATE( :TODAY , 'YYYYMMDD'))
  AND DATE_VALIDITE > DATE_TO_NUM(TO_DATE( :POS1 , 'YYYY-MM-DD HH24:MI:SS'))
  UNION ALL
  SELECT
    SICOVAM,
    AUDIT_DATE_MODIF - TO_DATE('01/01/1904','DD/MM/YYYY'),
    USERID,
    AUDIT_MODIF,
    'arbitrage',
    AUDIT_DATE_MODIF - TO_DATE('01/01/1904','DD/MM/YYYY')
  FROM AUDIT_ARBITRAGE
  WHERE
    1=1
    AND AUDIT_DATE_MODIF - TO_DATE('01/01/1904','DD/MM/YYYY') >= DATE_TO_NUM(TO_DATE( :TODAY , 'YYYYMMDD'))
    AND AUDIT_DATE_MODIF - TO_DATE('01/01/1904','DD/MM/YYYY') > DATE_TO_NUM(TO_DATE( :POS2 , 'YYYY-MM-DD HH24:MI:SS'))
) ADT
LEFT JOIN TITRES INSTRUMENT
  ON ADT.SICOVAM = INSTRUMENT.SICOVAM
LEFT JOIN AFFECTATION ALLOTMENT
  ON INSTRUMENT.AFFECTATION = ALLOTMENT.IDENT
LEFT JOIN RISKUSERS RU
  ON ADT.USERIDENT = RU.IDENT
WHERE
  1=1
  AND ALLOTMENT.LIBELLE  IN ( 'Indices', 'Listed Stock Options', 'OTC Options', 'Futures', 'Accumulators', 'Warrants', 'CBBC', 'SBL', 'Index Futures', 'Listed Index Options', 'Barrier Options', 'Loans and Deposits', 'Funding Notes', 'BULLELN', 'BEARELN', 'KOELN', 'Digital Options', 'DIGIELN', 'Fixed Cpn Note', 'Fixed Cpn Swap', 'Range Accrual Note', 'Range Accrual Swap', 'AutoCall Note', 'AutoCall Swap', 'Decumulators', 'WOELN', 'Accumulator Note', 'Bonus Cash Note', 'Bonus En Note', 'Bonus En Swap', 'Snball Note', 'Snball Swap' )
  AND MODIF = 2
ORDER BY MODIFIED_TIMESTAMP DESC