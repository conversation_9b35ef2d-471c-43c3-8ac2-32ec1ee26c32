SELECT 'CBBC', REF.sophis_ident as <PERSON><PERSON><PERSON><PERSON>eference,
  ref.VAL<PERSON> as GT<PERSON><PERSON><PERSON>,
  t.libelle as name,
  t.PRIXEXER as strike,
  t.finper as expiry,
  CASE t.typepro
    WHEN 1
    THEN 'C'
    ELSE 'P'
  END as CallPut,  
  underlyer.VALUE as Underlying_GTJARef,
  t.PROPORTION as conversionRation,
  current_date,
  devise_to_str(t.DEVISEAC) as currency,
  c.valeur as CallPrice  
FROM EXTRNL_REFERENCES_INSTRUMENTS ref
JOIN TITRES t
ON ref.SOPHIS_IDENT=t.sicovam
AND t.affectation IN
  (SELECT ident FROM AFFECTATION WHERE Libelle IN ('CBBC')
  )
LEFT JOIN EXTRNL_REFERENCES_INSTRUMENTS underlyer ON t.codesj = underlyer.SOPHIS_IDENT
and underlyer.REF_IDENT=2
left join Clause c on 
t.sicovam = c.sicovam and DATEDEB > TO_DATE('19040101', 'YYYYMMDD')
WHERE finper >= to_date(:today, 'YYYYMMDD')