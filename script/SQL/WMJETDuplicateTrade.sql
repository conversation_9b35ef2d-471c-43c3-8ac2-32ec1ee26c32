select refcon, CMPT_ORDRE, INFOSBACKO<PERSON><PERSON><PERSON>, cours, QUAN<PERSON><PERSON> from SOPHIS_PRD.JOIN_POSITION_HISTOMVTS where cmpt_ordre in (
select cmpt_ordre from (
select cmpt_ordre, count(*) as cnt from SOPHIS_PRD.JOIN_POSITION_HISTOMVTS where trunc(DATENEG)=to_date(:today, 'YYYYMMDD')
and creation=2 and BACKOFFICE in
  (SELECT id
  FROM BO_KERNEL_STATUS
  WHERE id IN
    (SELECT kernel_status_id
    FROM BO_KERNEL_STATUS_COMPONENT
    WHERE KERNEL_STATUS_GROUP_ID IN
      (SELECT MAX(id)
      FROM BO_KERNEL_STATUS_GROUP
      WHERE Name = 'All But Cancelled'
      GROUP BY name
      )
    ))
 group by cmpt_ordre order by cmpt_ordre)
where cnt > 1) order by cmpt_ordre