insert into HISTORIQUE (S<PERSON><PERSON><PERSON><PERSON>, JOUR, D)
select sicovam, to_date(:today, 'YYYYMMDD'), D from Historique where JOUR=TO_DATE(:lastBusinessDate, 'YYYYMMDD') and D is not null
and sicovam in (select sicovam from titres where affectation in (select ident from affectation where Libell<PERSON> ='Shares')) and sicovam not in (
select sicovam from historique where jour=to_date(:today, 'YYYYMMDD'))
