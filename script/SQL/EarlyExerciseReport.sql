SELECT 	sophis_pos.opcvm, 
		sophis_pos.sicovam, 
		sophis_pos.quantity, 
		sophis_pos.name, 
		instrument.product_name, 
		instrument.strike,
		instrument.expiration_date, 
		instrument.theo, 
		instrument.HISTDATE,
		instrument.last, 
		instrument.dividend, 
		instrument.next_dividend_date, 
		instrument.call_put, 
		instrument.hibor_last, 
		instrument.day_to_expiry, 
		instrument.financial_cost, 
		instrument.UNDERLYER_NAME, 
		instrument.RHS, 
		instrument.LHS, 
		instrument.UNDERLYER_SICOVAM
FROM
  ( -- Check Portfolio level 
    SELECT 	pos.opcvm,
			pos.sicovam,
			quantity,
			f.name
	FROM
		-- Active position in Sophis under HK FP Ledger
        (
            SELECT 	trade.mvtident,
                    trade.sicovam,
                    trade.opcvm,
                    SUM(trade.QUANTITE) AS quantity
            FROM JOIN_POSITION_HISTOMVTS trade,
                TITRES instrument,
                (SELECT *
                    FROM
                        (SELECT level AS lvl,
                                ident,
                                name
                        FROM folio
                            START WITH ident       = 13854
                            CONNECT BY prior ident = mgr
                        )
                ) p
            WHERE trade.sicovam = instrument.sicovam
            AND p.ident = trade.opcvm
            GROUP BY trade.mvtident,
                trade.sicovam,
                trade.opcvm
        ) pos, FOLIO f
	WHERE pos.opcvm=f.ident
	AND quantity  != 0
	ORDER BY name
   ) sophis_pos,
   -- instrument lookup
   (
	SELECT 	SERIES,
			UNDERLYER_SICOVAM,
			UNDERLYER_NAME,
			SICOVAM,
			PRODUCT_NAME,
			STRIKE,
			EXPIRATION_DATE,
			THEO,
			HISTDATE,
			LAST,
			DIVIDEND,
			Next_Dividend_date,
			CALL_PUT,
			HIBOR_LAST,
			DAY_TO_EXPIRY,
			FINANCIAL_COST,
			LHS,
			RHS
	FROM
		(
		    -- map the listed stock option with underlyer
			SELECT 	option_series.EXTENDEDMNEMO AS series,
					underlyer_titre.VALUE AS UNDERLYER_NAME,
					underlyer_titre.SOPHIS_IDENT AS underlyer_sicovam,
					listedOption.SICOVAM,
					listedOption.LIBELLE,
					--build a column for listed stock option [Series + Expiration Date + C | P + Strike]
					option_series.EXTENDEDMNEMO	||' '||TO_CHAR(listedOption.FINPER, 'DD/MM/YY')||' '||	CASE listedOption.typepro
						WHEN 2
							THEN 'C'
							ELSE 'P'
						END
					||' '||listedOption.prixexer AS product_name,
					listedOption.prixexer AS strike,
					listedOption.TYPE,
					listedOption.FINPER AS expiration_date,
					option_histo.T AS theo,
					option_histo.JOUR AS HistDate,
					option_series.sicovam AS underlyer,
					underlyer_histo.D AS last,
					SUM(d.VALEUR) AS dividend,              --sum of eligible dividends
					min(d.datediv) AS Next_Dividend_date,   --next dividend date
					listedOption.typepro AS call_put,
					hibor.D AS hibor_last,
					(date_to_num(listedOption.finper)-TRUNC(date_to_num(CURRENT_DATE))) AS day_to_expiry, -- num of day to be expired
					1.4 AS financial_cost,  --fixed financial cost
					-- LHS value
					CASE listedOption.typepro
						WHEN 2
							THEN option_histo.T
							ELSE option_histo.T-listedOption.prixexer
						END AS LHS,

					-- RHS value
					CASE listedOption.typepro
						WHEN 2
							THEN ((underlyer_histo.D - listedOption.prixexer) + SUM(d.valeur))
							ELSE ((listedOption.prixexer - underlyer_histo.D) * (((hibor.D+1.4) * (date_to_num(listedOption.finper) - TRUNC(date_to_num(CURRENT_DATE)))) / 365)) -- finance cost = 1.4 and Number of Days = 365
						END AS RHS
			FROM titres listedOption LEFT JOIN historique option_histo --lookup the theo price for the option
					        ON listedOption.sicovam = option_histo.sicovam
					        --AND TRUNC(option_histo.jour)= TRUNC(CURRENT_DATE-1)
					        AND TRUNC(option_histo.jour) = TO_DATE( :lastPriceDate, 'YYYYMMDD')
				        LEFT JOIN MO_SUPPORT option_series --map option with underlying
					        ON option_series.mnemo = listedOption.codesj
				        LEFT JOIN dividende d --lookup dividend table
					        ON d.sicovam = option_series.sicovam
					        AND d.datediv > CURRENT_DATE
					        AND d.datediv <= listedOption.finper
				        LEFT JOIN historique underlyer_histo --lookup last price for underlyer
					        ON option_series.sicovam = underlyer_histo.sicovam
					        AND TRUNC(underlyer_histo.jour) = TO_DATE( :reportDate , 'YYYYMMDD')
				        LEFT JOIN EXTRNL_REFERENCES_INSTRUMENTS underlyer_titre  --lookup GTJA Reference for underlyer
					        ON option_series.sicovam = underlyer_titre.sophis_ident
					        AND underlyer_titre.ref_ident = 3, -- GTJA Ref
				historique hibor
			WHERE listedOption.affectation IN (SELECT ident FROM AFFECTATION WHERE LIBELLE IN ('Listed Stock Options'))
			AND listedOption.finper > CURRENT_DATE
			AND listedOption.devisectt IN (SELECT codedevise FROM marche WHERE LIBELLE = 'HongKong Stock Exchange')
			AND d.sicovam = option_series.sicovam
			AND option_histo.T IS NOT NULL
			AND hibor.sicovam = 67657408  --Hibor Sicovam
			--AND TRUNC(hibor.jour) = TRUNC(CURRENT_DATE-1)
			AND TRUNC(hibor.jour) = TO_DATE( :lastPriceDate, 'YYYYMMDD')
			GROUP BY listedOption.SICOVAM,
				listedOption.LIBELLE,
				underlyer_titre.VALUE,
				underlyer_titre.SOPHIS_IDENT,
				listedOption.prixexer,
				listedOption.TYPE,
				listedOption.FINPER,
				option_histo.T,
				option_histo.JOUR,
				option_series.sicovam,
				underlyer_histo.D,
				listedOption.typepro,
				option_series.EXTENDEDMNEMO,
				hibor.D      
			ORDER BY sicovam
		)
	WHERE LHS<RHS) instrument
WHERE sophis_pos.sicovam=instrument.sicovam ORDER BY instrument.UNDERLYER_NAME, instrument.strike