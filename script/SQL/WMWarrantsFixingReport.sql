
SELECT c.sicovam,
      ref.value as GTJARef,
      min(c.datefin) as first_fixing_date,
      agg.LIBELLE as Name,
      agg.PRIXEXER as Strike ,
      agg.FINPER as Maturity_Date,
      case agg.TYPEPRO when 1 THEN 'C' ELSE 'P' END as CALL_PUT ,
      agg.value as Underlyer,
      agg.num as Remaining_Fixing_Date
FROM
clause c, extrnl_references_instruments ref,
(SELECT c.sicovam, t.LIBELLE, t.PRIXEXER, t.FINPER, t.TYPEPRO, underlyer.value, count(*) as num FROM clause c left join titres t on
c.sicovam = t.sicovam and t.affectation in (select ident from affectation where libelle in ('Warrants')), EXTRNL_REFERENCES_INSTRUMENTS underlyer
where c.datefin >= trunc(CURRENT_DATE)
and t.codesj = underlyer.sophis_ident and underlyer.ref_ident=3
group by c.sicovam, t.<PERSON>, t.<PERSON>IX<PERSON><PERSON>, t.<PERSON>, t.TYPEP<PERSON>, underlyer.value) agg
where c.sicovam = agg.sicovam
and ref.sophis_ident=agg.sicovam
and agg.LIBELLE is not null
and ref.ref_ident=3
and c.datefin!=to_date('19040101', 'YYYYMMDD')
and c.datefin <= trunc(CURRENT_DATE)
and agg.num > 0
group by ref.value,
c.sicovam,
agg.num,
agg.LIBELLE,
agg.PRIXEXER,
agg.FINPER,
agg.TYPEPRO,
agg.value
order by agg.value
