select DEVISE_TO_STR(h.sicovam), <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, h<PERSON><PERSON>, h.<PERSON>, h<PERSON><PERSON> from historique h left join titres t  on h.sicovam=t.sicovam where
h.jour = trunc(sysdate) and (h.sicovam < 60000000) union 
select * from (select t.REFERENCE, h.JOUR, h.B, h.H, h.D, h<PERSON><PERSON> from historique h left join titres t  on h.sicovam=t.sicovam where 
h.sicovam=(select sicovam from titres where reference in ('MXCNA50C')) and h.D is not null order by jour desc) where rownum=1