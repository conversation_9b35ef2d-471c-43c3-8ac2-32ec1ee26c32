select t.refcon,
t.sicovam,
instrument.LIBELLE as "Instrument Name",
t.dateneg as "Trade Date",
t.dateval as "Value Date",
t.INFOS as "Remark",
r.dateech as "Coupon Date"
from JOIN_AUDITPOS_AUDIT_MVT t left join amortissement r on t.sicovam = r.sicovam
and t.version=1 and trunc(t.datemodif)=trunc(current_date)
left join titres instrument on t.sicovam = instrument.sicovam
left join JOIN_POSITION_HISTOMVTS ht on ht.refcon=t.refcon
where
t.version=1 and trunc(t.datemodif)=trunc(current_date) and 
t.refcon in (select sophis_ident from EXTRNL_REFERENCES_TRADES
where value like 'TOMS%') and t.dateneg <= r.dateech and r.dateech <= t.dateval
and ht.backoffice in (select comp.kernel_status_id from BO_KERNEL_STATUS_COMPONENT comp left join BO_KERNEL_STATUS_GROUP grp on comp.KERNEL_STATUS_GROUP_ID=grp.ID and grp.RECORD_TYPE=1
where grp.NAME='All But Cancelled')
order by refcon