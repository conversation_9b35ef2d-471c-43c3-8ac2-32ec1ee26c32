SELECT trade.dateneg,
  trade.dateval,
  be.name,
  trade.dateneg,
  ru.name,
  ks.name,
  (trade.cours * trade.quantite),
  trade.cours,
  trade.quantite,
  entity.name,
  portfolio.name,
  trade.refcon,
  counterparty.REFERENCE,
  counterparty.name,
  trade.INFOS,
  trade.INFOSBACKOFFICE
FROM JOIN_POSITION_HISTOMVTS trade
LEFT JOIN business_events be
ON trade.type=be.id
LEFT JOIN riskusers ru
ON trade.OPERATEUR=ru.ident
LEFT JOIN tiers entity
ON entity.ident=trade.entite
LEFT JOIN folio portfolio
ON portfolio.ident=trade.opcvm
left join tiers counterparty
on counterparty.ident=trade.CONTRE<PERSON><PERSON><PERSON>
left join BO_KERNEL_STATUS ks
on ks.ID=trade.BACKOFFICE
WHERE trade.sicovam=:sicovam
AND trade.type=1
AND trade.BACKOFFICE in
(select kernel_status_id from BO_KERNEL_STATUS_COMPONENT where kernel_status_group_id in (Select id from BO_KERNEL_STATUS_GROUP where name = 'All But Cancelled'
and record_type=1))