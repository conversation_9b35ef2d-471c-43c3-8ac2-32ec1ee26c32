select trade.dateneg, allot.libelle, sum(abs(trade.cours*trade.quantite))
from JOIN_POSITION_HISTOMVTS trade
left join titres ins on trade.sicovam=ins.sicovam
left join affectation allot on ins.affectation=allot.ident
where dateneg >= to_date(:fromDate, 'YYYYMMDD')
and dateneg <= to_date(:ToDate, 'YYYYMMDD')
and creation = 2 and allot.libelle in ('Warrants', 'CBBC', 'Shares','ETF')
group by trade.dateneg, allot.libelle order by dateneg, libelle