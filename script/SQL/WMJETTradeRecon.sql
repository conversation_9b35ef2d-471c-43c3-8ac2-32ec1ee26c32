SELECT
  CMPT_ORDRE AS JET_ORDER_ID,
  cours           AS Trade_Price,
  QUANTITE        AS Quantity,
  BACKOFFIC<PERSON>,
  REFCON
FROM JOIN_POSITION_HISTOMVTS
WHERE TRUNC(dateneg)=to_date(:today, 'YYYYMMDD')
AND CREATION        =2
AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in
  (SELECT id
  FROM BO_KERNEL_STATUS
  WHERE id IN
    (SELECT kernel_status_id
    FROM BO_KERNEL_STATUS_COMPONENT
    WHERE KERNEL_STATUS_GROUP_ID IN
      (SELECT MAX(id)
      FROM BO_KERNEL_STATUS_GROUP
      WHERE Name = 'All But Cancelled'
      GROUP BY name
      )
    ))
