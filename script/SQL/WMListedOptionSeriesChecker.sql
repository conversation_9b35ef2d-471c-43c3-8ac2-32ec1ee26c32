SELECT listedOption.mnemo,
  listedOption.sicovam,
  listedOption.marche,
  listedOption.EXTENDEDMNEMO,
  t.L<PERSON>,
  MIN(oldstrike),
  MAX(oldstrike),
  strike.echeance,
  num_to_date(maturity.echeance) AS maturity,
  md.d                           AS Last,
  ref.value as underlyerTicker
FROM mo_support listedOption
JOIN titres t
ON listedOption.sicovam = t.sicovam
LEFT JOIN mo_ajuststrike2 strike
ON listedOption.mnemo = strike.mnemo
JOIN MO_echeance maturity
ON strike.echeance = maturity.ident
JOIN HISTORIQUE md
ON md.sicovam = t.sicovam
AND md.jour   =to_date(:today, 'YYYYMMDD')
LEFT JOIN EXTRNL_REFERENCES_INSTRUMENTS ref
ON listedOption.sicovam = ref.SOPHIS_IDENT
AND ref.REF_IDENT       =3
WHERE maturity.echeance > date_to_num(to_date(:today, 'YYYYMMDD'))
GROUP BY listedOption.mnemo,
  listedOption.sicovam,
  listedOption.marche,
  listedOption.EXTENDEDMNEMO,
  t.LIBELLE,
  strike.echeance,
  maturity.echeance,
  md.D,
  ref.VALUE
ORDER BY listedOption.EXTENDEDMNEMO,
  maturity