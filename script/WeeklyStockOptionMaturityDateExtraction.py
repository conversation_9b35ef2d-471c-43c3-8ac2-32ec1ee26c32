## Date         Name                       Detail
# A script to generate the Weekly Stock Option Maturity Date
#
#==========================================================================================================================
#
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import cx_Oracle
import datetime

dateFormat = "YYYYMMDD"

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

outputFile = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('OutputFileName')
reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('reportFolder')
csvReportName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('CSVFilename')
csvReportExt = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('CSVFileExt')
csvReportHeader = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('CSVReportHeader')

businessDate = datetime.date.today()
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')

logger.info('SQL = %s', SQL)
with open(SQL) as file: sql = file.read()

businessDate = businessDate.strftime('%Y%m%d')
logger.info('Report Date = %s', businessDate)
csvReportName = csvReportName + "_" + businessDate + csvReportExt
logger.info("CSV Report File Name: " + csvReportName)
sophisDb = SophisDBConnection()

dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance())

logger.info(dbConn)
cursor = dbConn.cursor()
cursor.execute(sql)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))

with open(reportFolder+"/" + csvReportName, 'w+') as outputFn:
    outputFn.write(csvReportHeader)
    outputFn.write("\n")
    if (len(results) > 0):
        for internalref in results:
            logger.info(internalref)
            logger.info(internalref[0])
            outputFn.write(internalref[0])
            outputFn.write("\n")
