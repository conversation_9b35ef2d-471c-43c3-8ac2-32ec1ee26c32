class JetInstrument:
    def __init__(self, ticker, name, allotment, currency, underlying, expiry, callPut, conversionRatio, strike, callPrice):
        self.ticker = ticker
        self.allotment = allotment
        self.callPrice = callPrice
        self.name = name
        self.currency = currency
        self.underlying = underlying
        self.expiryDate = expiry
        self.callPut = callPut
        self.conversionRatio = conversionRatio
        self.strike = strike

    def __str__(self):
        return f'[Instrument={self.ticker}, name={self.name}, currency={self.currency}, underlying={self.underlying}, expiry={self.expiryDate}, callPut={self.callPut}, conversionRatio={self.conversionRatio}, strike={self.strike}]'

    def __repr__(self):
        return f'[Instrument={self.ticker}, name={self.name}, currency={self.currency}, underlying={self.underlying}, expiry={self.expiryDate}, callPut={self.callPut}, conversionRatio={self.conversionRatio}, strike={self.strike}]'