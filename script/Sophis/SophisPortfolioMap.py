import csv
import logging
from .SophisPortfolioRecord import SophisPortfolioRecord


class SophisPortfolioMap:
    def __init__(self, file=None):
        if ( file == None ):
            print("No input file")
        else:
            self.portfolioMap = dict()
            try:
                with open(file, newline='') as csvfile:
                    first = True
                    reader = csv.reader(csvfile, delimiter=',')
                    for row in reader:
                        if ( first == True ):
                            first = False
                        else:
                            instrumentName = row[1]
                            deltaInPercent = row[11]
                            quantity = row[5]
                            globalDelta = row[9]
                            globalGamma = row[24]
                            record = SophisPortfolioRecord(instrumentName, deltaInPercent, quantity, globalDelta, globalGamma)
                            self.portfolioMap[instrumentName] = record
            except BaseException as ex:
                logging.error(ex)

    def getPortfolioMap(self):
        return self.portfolioMap