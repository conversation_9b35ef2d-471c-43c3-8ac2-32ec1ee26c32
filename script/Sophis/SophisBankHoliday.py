import logging
from datetime import timedelta

class SophisBankHoliday:
    def __init__(self, dbConn, sqlFile, parameter):
        self.__currencyRepository__ = dict()
        cursor = dbConn.cursor()
        with open(sqlFile) as file: sql = file.read()
        logging.info("SQL query = %s", sql)
        cursor.execute(sql, today=parameter)
        results = cursor.fetchall()
        logging.info('Records = [ %i ]', len(results))
        for record in results:
            #logging.info(' %s ', record[0])
            currency = record[0]
            date = record[1]
            if ( currency in self.__currencyRepository__ ):
                self.__currencyRepository__[currency].append(date)
            else:
                holidayList = list()
                self.__currencyRepository__[currency] = holidayList
                self.__currencyRepository__[currency].append(date)

        for key in self.__currencyRepository__.keys():
            tempList = self.__currencyRepository__[key]
            logging.info(tempList)
        cursor.close()

    def IsWeekend(self, date):
        weekOfDay = date.strftime('%a')
        if ( weekOfDay == 'Sat' or weekOfDay == 'Sun'):
            return True
        return False

    def IsHoliday(self, currency, date):
        if ( self.IsWeekend(date) == False and currency in self.__currencyRepository__):
            for item in self.__currencyRepository__[currency]:
                if ( item == date ):
                    return True
        else:
            return False

    def oneDayBefore(self, currency, date):
        newDate = date - timedelta(1)
        if ( self.IsHoliday(currency, newDate) ):
            return self.oneDayBefore(currency, newDate)
        else:
            return newDate


