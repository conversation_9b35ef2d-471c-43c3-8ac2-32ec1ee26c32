class SophisPortfolioRecord:
    def __init__(self, name, deltaInPercent, quantity, globalDelta, globalGamma):
        self.name = name
        self.quantity = quantity
        self.deltaInPercent = deltaInPercent
        self.globalDelta = globalDelta
        self.globalGamma = globalGamma


    def __str__(self):
        return f'[name={self.name}, quantity={self.quantity}, deltaInPercent={self.deltaInPercent}, globalDelta={self.globalDelta}, globalGamma={self.globalGamma}]'

    def __repr__(self):
        return f'[name={self.name}, quantity={self.quantity}, deltaInPercent={self.deltaInPercent}, globalDelta={self.globalDelta}, globalGamma={self.globalGamma}]'