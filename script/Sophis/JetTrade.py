##
##  Name        Date                Description
##==========================================================================================================
##  <PERSON>  23-March-2021       Use Side as a order key to handle the Future Spread order
##
import logging

class JetTrade:
    def __init__(self, orderId, allotment, instrument, tradePrice, quantity, side, executionId):
        self.__executions__ = set()
        self.allotment = allotment
        self.orderId = orderId
        self.tradePrice = 0
        self.quantity = 0

        self.side = side
        self.match = False
        self.totalAmount = 0
        self.__executions__ = set()
        self.addExecution(instrument, tradePrice, quantity, executionId)

    def matched(self):
        self.match = True

    def addExecution(self, instrument, price, quantity, executionId):
        if ( executionId not in self.__executions__):
            logging.info("%s Add Execution [%s] %s@%s ", self.orderId, executionId, price, quantity)
            self.quantity += float(quantity)
            self.totalAmount += float(price) * int(quantity)
            self.__executions__.add(executionId)
        else:
            logging.warning("Duplicated Trade %s skip order id[%s] quantity[%s]", executionId, self.orderId, quantity)
            logging.warning(self.__executions__)

    def getAveragePrice(self):
        return self.totalAmount / self.quantity

    def getSophisQuantity(self):
        if (self.side=='S'):
            return self.quantity * -1
        return self.quantity

    def __str__(self):
        return f'[Order={self.orderId}, allotment={self.allotment}, side={self.side}, price={self.tradePrice}, quantity={self.quantity}, sophis_quantity={self.getSophisQuantity()}, total amount={self.totalAmount}]'

    def __repr__(self):
        return f'[Order={self.orderId}, allotment={self.allotment}, side={self.side}, price={self.tradePrice}, quantity={self.quantity}, sophis_quantity={self.getSophisQuantity()}, total amount={self.totalAmount}]'