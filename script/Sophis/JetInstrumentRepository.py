import csv
from .JetInstrument import JetInstrument

class JetInstrumentRepository:
    def __init__(self, file=None):
        if ( file == None ):
            print("No input file")
        else:
            with open(file, newline='') as csvfile:
                first = True
                reader = csv.reader(csvfile, delimiter=',')
                self.instrumentRepository = dict()
                self.cbbc = dict()
                self.warrant = dict()
                for row in reader:
                    if ( first == True ):
                        first = False
                    else:
                        #ticker, name, allotment, currency, underlying, expiry, callPut, conversionRatio, strike, callPrice
                        ticker = row[0]
                        name = row[2]
                        allotment = row[3]
                        currency = row[6]
                        underlying = row[13]
                        expiry = row[16]
                        strike = row[18]
                        callPut = row[19]
                        conversionRatio = row[22]
                        callPrice = row[26]

                        if ( allotment not in self.instrumentRepository):
                            self.instrumentRepository[allotment] = dict()

                        dictInst = self.instrumentRepository[allotment]

                        if ( ticker in  dictInst):
                            instrument = dictInst[ticker]
                        else:
                            #ticker, name, currency, underlying, expiry, callPut, conversionRatio, strike
                            instrument = JetInstrument(ticker, name, allotment, currency, underlying, expiry, callPut, conversionRatio, strike, callPrice)
                            dictInst[ticker] = instrument

            for item in self.instrumentRepository.keys():
                print(item)
            print('Total number of instrument = ', len(self.instrumentRepository))

    def getJetInstrumentRepository(self):
        return self.instrumentRepository
