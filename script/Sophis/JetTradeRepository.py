##
##  Name        Date                Description
##==========================================================================================================
##  <PERSON>  23-March-2021       Use Side as a order key to handle the Future Spread order
##
import csv
import logging
from .JetTrade import JetTrade

class JetTradeRepository:
    def __init__(self, file=None):
        if ( file == None ):
            print("No input file")
        else:
            with open(file, newline='') as csvfile:
                first = True
                reader = csv.reader(csvfile, delimiter=',')
                self.tradeRepository = dict()
                for row in reader:
                    if ( first == True ):
                        first = False
                    else:
                        instrumentName = row[2]
                        side = row[3]
                        quantity = row[6]
                        price = row[7]
                        executionId = row[9]
                        orderId = row[10]
                        allotment = row[1]
                        orderKey = orderId+'@'+side
                        if ( orderKey in  self.tradeRepository):
                            logging.info('Append Order [%s]', orderKey)
                            trade = self.tradeRepository[orderKey]
                            trade.addExecution(instrumentName, price, quantity, executionId)
                        else:
                            logging.info('New Order [%s]', orderKey)
                            # orderId, instrument, tradePrice, quantity, side
                            trade = JetTrade(orderId, allotment, instrumentName, price, quantity, side, executionId)
                            self.tradeRepository[orderKey] = trade

            for item in self.tradeRepository.items():
                logging.info(item)
            logging.info('Total number of trades = [%d]', len(self.tradeRepository))

    def getJetTradeRepository(self):
        return self.tradeRepository