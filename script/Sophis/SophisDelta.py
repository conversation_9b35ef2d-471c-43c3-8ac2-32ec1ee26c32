import csv

class SophisDelta:
    def __init__(self, file=None):
        if ( file == None ):
            print("No input file")
        else:
            with open(file, newline='') as csvfile:
                first = True
                reader = csv.reader(csvfile, delimiter=',')
                self.delta = dict()
                for row in reader:
                    if ( first == True ):
                        first = False
                    else:
                        instrumentName = row[1]
                        deltaInPercent = row[11]
                        self.delta[instrumentName] = deltaInPercent

    def getDelta(self):
        return self.delta
