import logging
import zipfile
import datetime
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil

def GetDate(type:None):
    today = datetime.date.today()
    Day = today.strftime('%A')
    if type == 'LastBusinessDate':
        if Day == 'Monday':
            print("Delta is ", 3)
            no_of_days = datetime.timedelta(days=3)
        else:
            print("Delta is ", 1)
            no_of_days = datetime.timedelta(days=1)
        last = today - no_of_days
        return last.strftime('%Y%m%d')
    return today.strftime('%Y%m%d')

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
ZipFileFolder = PropertiesUtil.configObject.GetConfigProperties()['Zip'].get('ZipFileFolder')

if ZipFileFolder is None:
    ZipFileFolder = ''

ZipFileName = PropertiesUtil.configObject.dd()['Zip'].get('ZipFileName')
ZipFiles = PropertiesUtil.configObject.GetConfigProperties()['Zip'].get('ZipFiles')
UseDate = PropertiesUtil.configObject.GetConfigProperties()['Zip'].get('UseDate')
YYYYMMDD = GetDate(UseDate)
logging.info('Dates[' +YYYYMMDD +']')
logging.info('ZipFileName[' +ZipFileName +']')
logging.info('ZipFiles[' +ZipFiles +']')
files = ZipFiles.split(';')
logging.info(files)
ZipFileName = ZipFileName.replace('YYYYMMDD', YYYYMMDD)

with zipfile.ZipFile(ZipFileFolder + ZipFileName, mode="w", compression=zipfile.ZIP_DEFLATED ) as archive:
     for file in files:
        try:
            file = file.replace('YYYYMMDD', YYYYMMDD)
            fileWithoutDir = file[file.rindex('\\')+1:]
            archive.write(file, fileWithoutDir)
            logging.info('zip ['+fileWithoutDir+']')
        except FileNotFoundError as e:
            logging.error(e)
