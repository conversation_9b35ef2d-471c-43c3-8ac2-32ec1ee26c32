#
# files:
# RenameTreasurySmartCashNotesWithPosition.py
#
#
# Date        Name            Comment
# ============================================================================================
# 20220910    Raymond Lin     Initial commit

import sys
import os
from os.path import exists as file_exists
from datetime import datetime, timedelta
import socket

class Logging:
	def __init__(self):
		"""constructor"""
		self.__logFile = datetime.today().strftime('..\log\GenerateTreasurySmartCashNotesWithPosition.%Y_%m_%d.log')
		self.logger = open(self.__logFile, 'a')
		self.hostname = socket.gethostname()
		print('write to ' + self.__logFile)
		
	def log(self, message):
		"""append log message to sophis log file"""
		logDate = datetime.today().strftime('%Y/%m/%d-%H:%M:%S.%f')[:-3]
		self.logger.write('             info :          PythonScript@' + self.hostname + ' (00000.0000) : [' + logDate + '] : GenerateTreasurySmartCashNotesWithPositionPost.py : ' + message + '\n')
		
	def __del__(self):
		"""destructor"""
		self.logger.close()

def main() -> 0: 
	# create logger without change dir first
	logging = Logging()
	
	# change dir to GTJA_Reports\Treasury\out
	os.chdir('..\GTJA_Reports\Treasury\out')
	directory = os.getcwd()

	logging.log('change working directory ' + directory) 
	
	# prepare file names
	currDate = datetime.today().strftime('%y%m%d')
	prevDate = (datetime.now() - timedelta(1)).strftime('%y%m%d')

	# assemble the old and new file names
	currFile = currDate + '_Sophis_SmartCashNotes position.csv'
	prevFile =  prevDate + '_Sophis_SmartCashNotes position.csv'	 

	# rename file when exists
	try:
		if file_exists(currFile):
			logging.log(currFile + ' found in directory')
			if file_exists(prevFile):
				logging.log('prev file ' + prevFile + ' found, will overwrite it!')
				os.remove(prevFile)
			else:
				logging.log('rename file '+currFile+' -> ' + prevFile)
			
			# always try to rename file
			os.rename(currFile, prevFile)
			logging.log('completed, exit')
			return 0
		else:
			logging.log(currFile + ' not found, exit')
			return 1
	except Exception as e:
		logging.log('exception error: ' + str(e) + ', exit')
		return 2

if __name__ == '__main__':
	sys.exit(main())
