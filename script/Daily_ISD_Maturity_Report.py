#  Date         Name                      Detail
##==========================================================================================================================
# 20240505     Alvin Mak                  Generate the MO Daily Maturity Report.
#                                         1. Products with today's date < = Maturity Date within next 7 calendar days
#                                         2. Products with Maturity Date <= Today
#                                         3. Allotments: LNOTE - BONDS, LNOTE - EQUITY, PNOTE, TRS Bonds, TRS Bonds - Basket
#
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJAFormatter import GTJAFormatter
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import StringUtil
from GTJA import GTJAAttachmentEmailSender

import cx_Oracle
import datetime
import xlsxwriter
from xlsxwriter import Workbook
import os

""" program start """
logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

dateFormat = 'YYYYMMDD'
today = datetime.date.today()
logger.info('today = %s', today)
businessDate = datetime.date.today().strftime('%Y%m%d')

InputSQLFile1 = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('InputSQL1')
InputSQLFile2 = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('InputSQL2')
Template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Template')
ReportPath = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportPath')
ReportExcelFileName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportExcelFileName')
ReportHTMLFileName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportHTMLFileName')
OffsetDay = PropertiesUtil.configObject.GetConfigProperties()['Parameter'].get('OffsetDay')
logger.info('InputSQLFile = %s', InputSQLFile1)
logger.info('InputSQLFile = %s', InputSQLFile2)
logger.info('Template = %s', Template)
logger.info('ReportPath = %s', ReportPath)
logger.info('OffsetDay = %s', OffsetDay)

""" format date """
ReportExcelFileName = StringUtil.formatDate(ReportExcelFileName, today)
logger.info('ReportExcelFileName = %s', ReportExcelFileName)

ReportHTMLFileName = StringUtil.formatDate(ReportHTMLFileName, today)
logger.info('ReportHTMLFileName = %s', ReportHTMLFileName)

ExcelReportFullOutPath = os.path.join(ReportPath, ReportExcelFileName)
logger.info('ExcelReportFullOutPath = %s', ExcelReportFullOutPath)

HTMLReportFullOutPath = os.path.join(ReportPath, ReportHTMLFileName)
logger.info('HTMLReportFullOutPath = %s', HTMLReportFullOutPath)


sql_executor = lambda cur, sql, param: (
    cur.execute(sql) if param == 0 \
    else (cur.execute(sql, param_OffsetDay=OffsetDay) if param == 1 \
    else ValueError('Invalid SQL query input arguments!')))


def excel_generator(cursor, inputSQL, sqlParam, workbook, sheetName):
    try:
        sql_executor(cursor, inputSQL, sqlParam)

        logger.info('OffsetDay = %s', OffsetDay)
        dbResults = cursor.fetchall()
        worksheet = workbook.add_worksheet(sheetName)
        header = ['Book', 'Instrument Name', 'Maturity', 'Allotment', 'No of Securities', 'Counterparty Name']
        for idx, col in enumerate(header):
            worksheet.write(0, idx, col)  # Write the column name one time in a row

        if cursor.rowcount > 0:
            for r, row in enumerate(dbResults):
                for c, col in enumerate(row):
                    worksheet.write(r + 1, c, col)
                logger.info(row)
            logger.info('Complete Worksheet %s', sheetName)
        return dbResults
    except Exception as e:
        logger.info('%s', str(e))


def html_generator(dbResults, reportTitle):
    outputReport = ''
    if len(dbResults) > 0:
        outputReport += '<p style=\'color:red;font-size:20px;\'><b>' + reportTitle + '</b></p>'
        for internal in dbResults:
            book = internal[0]
            instrument_name = internal[1]
            maturity = internal[2]
            allotment = internal[3]
            no_of_securities = internal[4]
            counterparty_name = internal[5]
            outputReport += '<tr>'
            outputReport += '<td>' + book + '</td>'
            outputReport += '<td>' + instrument_name + '</td>'
            outputReport += '<td>' + maturity + '</td>'
            outputReport += '<td>' + allotment + '</td>'
            outputReport += '<td align=\'right\'>' + GTJAFormatter.formatAmount(no_of_securities, 0) + '</td>'
            outputReport += '<td>' + counterparty_name + '</td>'
            outputReport += '</tr>'
    else:
        outputReport += '<tr> <td> No Record </td></tr>'
    logger.info('Complete Report %s', reportTitle)
    return outputReport


def main():
    try:
        with open(InputSQLFile1, 'r') as SQLFile1:
            InputSQL1 = SQLFile1.read().replace('\n', ' ')
            InputSQL1 = InputSQL1.replace('\t', ' ')
            logger.info('InputSQL = %s', InputSQL1)
            SQLFile1.close()

        with open(InputSQLFile2, 'r') as SQLFile2:
            InputSQL2 = SQLFile2.read().replace('\n', ' ')
            InputSQL2 = InputSQL2.replace('\t', ' ')
            logger.info('InputSQL = %s', InputSQL2)
            SQLFile2.close()

        # Number of SQL input parameters
        sqlParam = [1, 0]
        # Excel Workbook
        workbook = xlsxwriter.Workbook(ExcelReportFullOutPath)
        # Excel worksheet names
        sheetName = ['Matured in ' + OffsetDay + ' Days', ' Already Matured']
        # HTML report display titles
        reportTitle = ['Products to be Matured in ' + OffsetDay + ' Days', 'Products Already Matured']

        sophisDb = SophisDBConnection()
        with cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                               sophisDb.getHost() + "/" + sophisDb.getDbInstance()) as dbConn:
            logger.info(dbConn)

            with dbConn.cursor() as cursor:
                dbResults1 = excel_generator(cursor, InputSQL1, sqlParam[0], workbook, sheetName[0])
                outputReport1 = html_generator(dbResults1, reportTitle[0])

                with open('template/' + Template) as file:
                    templateContent = file.read()
                    content1 = templateContent.replace('###Template1###', outputReport1)
                    content1 = content1.replace('###Template2###', '')
                    logger.info('Matured in ' + OffsetDay + ' Days Content')
                    logger.info(content1)

                dbResults2 = excel_generator(cursor, InputSQL2, sqlParam[1], workbook, sheetName[1])
                outputReport2 = html_generator(dbResults2, reportTitle[1])

                content2 = templateContent.replace('###Template1###', '')
                content2 = content2.replace('###Template2###', outputReport2)
				
                logger.info('Products Already Matured Content')
                logger.info(content2)
                outputFile = open(HTMLReportFullOutPath, 'w')
                outputFile.write(content1 + content2)

                workbook.close()
                outputFile.close()
                cursor.close()
                dbConn.close()

                # Send email with the Excel attachment
                GTJAAttachmentEmailSender.sendEmail(content1 + content2, ExcelReportFullOutPath, 'xlsx', businessDate, dateFormat)
    except Exception as e:
        logger.info('%s', str(e))


if __name__ == "__main__":
    main()
