## Date         Name                       Detail
# To generate the Bloomberg BCOL upload file for Operations.
#
# ==========================================================================================================================
# 20240920     Alvin Mak                 Initial Version
#

import json
import csv

from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from BCOL.BCOL_Parser import BCOL_Parser
import logging
import datetime

if __name__ == "__main__":

    dateFormat = "YYYYMMDD"

    logFileName = PropertiesUtil.configObject.GetLogFileName()
    log = GTJALogging(logFileName)
    logger = log.GetLogger()
    businessDate = datetime.date.today().strftime('%Y%m%d')
    input_file = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('InputFile')
    output_directory = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('OutputFolder')
    output_file = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('OutputFile')
    reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('reportFolder')
    input_file = input_file.replace('{date}', businessDate)

    with open(reportFolder+input_file, "r") as f:
        allPositions = json.load(f)

    BCOLParser = BCOL_Parser()
    items = BCOLParser.generate_positions(allPositions)
    logging.info('Number of BCOL Trades = %d', len(items))
    with open(output_directory + '/' + businessDate + '_' + output_file, 'w', newline='') as csvfile:
        csvWriter = csv.writer(csvfile, delimiter=',', quoting=csv.QUOTE_NONNUMERIC)
        for rec in items:
            csvWriter.writerow(rec)