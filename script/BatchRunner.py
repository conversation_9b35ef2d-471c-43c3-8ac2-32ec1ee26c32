## Date         Name                       Detail
##==========================================================================================================================
##20200605      Chris <PERSON>u                 This is a script to check if any dividend record is not correctly setup
##
##
from GTJA import PropertiesUtil
from GTJA.GTJALogging import GTJALogging
from GTJA.HtmlFileDiffHelper import HtmlFileDiffHelper
import datetime
import smtplib
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage
import os

dateFormat = "YYYYMMDD"

def sendEmail(content, today_str):
    logger.info("Try to send email")
    sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
    receiptent = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Receiptent')
    subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
    smptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
    smptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

    subject = subject.replace(dateFormat, today_str)

    subject = subject
    msg = EmailMessage()
    msg['From'] = sender
    msg['To'] = receiptent
    msg['Subject'] = subject
    #msg.add_header('Content-Type', 'text')
    msg.set_content(content)
    #msg.set_payload(content)
    logger.info("================================================================")
    #logger.info(msg.as_string())
    if (PropertiesUtil.configObject.IsPRD()):
        with smtplib.SMTP(smptHost, smptPort) as server:
            server.send_message(msg)
            server.quit()
    else:
        logger.info("No email sending for DEV or UAT")


logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

content = os.system('cmd /c "dir/w"')

today_str = datetime.date.today().strftime('%Y%m%d')
sendEmail(content, today_str)

