#  Date         Name                      Detail
##==========================================================================================================================
## 20210731     Alvin Mak                 Generate the MO Bond Redemption and Capital Report
#

from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import StringUtil

import cx_Oracle
import datetime
import csv
import os

""" program start """
logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
try:
    today = datetime.date.today()
    logger.info('today = %s', today)

    InputSQLFile = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('InputSQL')
    ReportPath = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportPath')
    ReportFileName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFileName')
    DeltaDay = PropertiesUtil.configObject.GetConfigProperties()['Parameter'].get('DeltaDay')
    logger.info('InputSQLFile = %s', InputSQLFile)
    logger.info('ReportPath = %s', ReportPath)
    logger.info('ReportFileName = %s', ReportFileName)
    logger.info('DeltaDay = %s', DeltaDay)

    """ format date """
    ReportFileName = StringUtil.formatDate(ReportFileName, today)
    logger.info('ReportFileName = %s', ReportFileName)

    ReportFullOutPath = os.path.join(ReportPath, ReportFileName)
    logger.info('ReportFullOutPath = %s', ReportFullOutPath)

    with open(InputSQLFile, 'r') as SQLFile:
        InputSQL = SQLFile.read().replace('\n', ' ')
        InputSQL = InputSQL.replace('\t', ' ')
        logger.info('InputSQL = %s', InputSQL)
        SQLFile.close()

    sophisDb = SophisDBConnection()
    with cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance()) as dbConn:
        logger.info(dbConn)

        with dbConn.cursor() as cursor:
            cursor.execute(InputSQL, param_DeltaDay=DeltaDay)
            logger.info('DeltaDay = %s', DeltaDay)
            results = cursor.fetchall()
            if cursor.rowcount > 0:
                with open(ReportFullOutPath, 'w') as CSVFile:
                    writer = csv.writer(CSVFile, delimiter=',', lineterminator="\n", quoting=csv.QUOTE_NONNUMERIC)
                    writer.writerow([i[0] for i in cursor.description])  # Header row
                    for row in results:
                        writer.writerow(row)
                        logger.info(row)
                    results.close()
                    dbConn.close()
                    CSVFile.close()

except Exception as e:
    logger.info('%s', str(e))
