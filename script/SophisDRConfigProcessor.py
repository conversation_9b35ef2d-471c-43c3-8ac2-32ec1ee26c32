import os
import sys
import logging
import datetime

PROD_SERVERS = ['SophisCSN1', 'sophiscsn1', 'SophisISN1', 'SophisASN1', 'SophisCFN1', 'SophisOSN1']
DR_SERVERS = ['SophisCSDRN1', 'sophiscsdrn1', 'SophisISDRN1', 'SophisASDRN1', 'SophisCFDRN1', 'SophisOSDRN1']

print(PROD_SERVERS)

def ProcessSophisConfig(filename):
    sourceFile = open(filename, 'r')
    content = sourceFile.read()
    logging.info('Convert File [%s]', filename)
    for idx in range(len(PROD_SERVERS)):
        content = content.replace(PROD_SERVERS[idx], DR_SERVERS[idx])
    sourceFile.close()
    DRFile = open(filename, 'w')
    DRFile.write(content)
    DRFile.close()

def SubdirectoryProcessing(directory):
    for entry in os.scandir(directory):
        if ( entry.is_file() == False):
            logging.info('File [%s]', entry.path)
            SubdirectoryProcessing(entry.path)
        elif (entry.path.endswith("config") and entry.is_file()):
            ProcessSophisConfig(entry.path)
        else:
            logging.info('Skip File [%s]', entry.path)

folder = 'log'
today_str = datetime.date.today().strftime('%Y%m%d')
logFile = 'SophisDRConfigProcessor.txt'

logging.basicConfig(level=logging.INFO,
                  format="%(asctime)s %(name)-12.12s [%(threadName)-12.12s] [%(levelname)-5.5s]  %(message)s",
                  handlers=[logging.StreamHandler(),
                            logging.FileHandler(
                                folder + os.sep + today_str + "_" + logFile, mode="w")])
if len(sys.argv) > 1:
    directory = sys.argv[1]
    logging.info('Process directory = %s', directory)
    SubdirectoryProcessing(directory)
else:
    logging.error('No Directory!')
    exit(-1)
exit(0)