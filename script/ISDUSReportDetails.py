## Date         Name                       Detail
# This is a python report to check the notional breakdown details for the US client reporting.
# Email alert is sent when the targeted threshold rate is reached.
#
# ==========================================================================================================================
# 20230324      Alvin Mak                 Initial version
#

from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJAFormatter import GTJAFormatter
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from GTJA import StringUtil
from GTJA import GTJAAttachmentEmailSender
import cx_Oracle
import datetime
import xlsxwriter

dateFormat = 'YYYYMMDD'

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()

businessDate = datetime.date.today()
businessDate = businessDate.strftime('%Y%m%d')


class NotionalDetails:
    def __init__(self, template, SQLRecord, SQLDetail, outputFilePrefix, defaultFX,
                 ExcelReportFullOutPath, HTMLReportFullOutPath):
        logger.info('NotionalDetail Parent Class: __init__')
        self.template = template
        self.SQLRecord = SQLRecord
        self.SQLDetail = SQLDetail
        self.outputFilePrefix = outputFilePrefix
        self.defaultFX = defaultFX
        self.ExcelReportFullOutPath = ExcelReportFullOutPath
        self.HTMLReportFullOutPath = HTMLReportFullOutPath

    def ExcelGenerator(self, dbResults, workbook):
        try:
            recCount = len(dbResults)
            sheetName = 'Notional Details'
            worksheet = workbook.add_worksheet(sheetName)
            header = ['Counterparty Name', 'Trade Date', 'Instrument Name', 'Notional (USD)', 'Notional Native']
            for idx, col in enumerate(header):
                worksheet.write(0, idx, col)  # Write the column name one time in a row

            # Write data from database results to Excel
            for r, row in enumerate(dbResults):
                for c, col in enumerate(row):
                    worksheet.write(r + 1, c, col)

            # Calculate the summation
            totallUSD = sum([row[header.index('Notional (USD)')] for row in dbResults])
            totalNative = sum([row[header.index('Notional Native')] for row in dbResults])

            # Write summation to Excel
            worksheet.write(recCount + 1, 2, 'Total Volume')
            worksheet.write(recCount + 1, header.index('Notional (USD)'), totallUSD)
            worksheet.write(recCount + 1, header.index('Notional Native'), totalNative)

            logger.info('Complete Worksheet %s', sheetName)
        except Exception as e:
            logger.info('%s', str(e))

    def HtmlGenerator(self, dbResults):
        try:
            outputReport = ''
            totalNotionalUSD = 0
            totalNotionalNative = 0

            if len(dbResults) > 0:
                for internalDetail in dbResults:
                    counterParty = internalDetail[0]
                    tradeDate = internalDetail[1]
                    instrumentRef = internalDetail[2]
                    notionalUSD = internalDetail[3]
                    notionalNative = internalDetail[4]
                    totalNotionalUSD += notionalUSD
                    totalNotionalNative += notionalNative
                    outputReport += '<tr>'
                    outputReport += '<td>' + counterParty + '</td>'
                    outputReport += '<td>' + tradeDate.strftime('%d-%b-%Y') + '</td>'
                    outputReport += '<td>' + instrumentRef + '</td>'
                    outputReport += '<td align=\'right\'>' + "{:,.{}f}".format(notionalUSD, 2) + '</td>'
                    outputReport += '<td align=\'right\'>' + "{:,.{}f}".format(notionalNative, 2) + '</td>'
                    outputReport += '</tr>'

            outputReport += '<tr><td></td><td></td>'
            outputReport += '<td>Total Volume</td>'
            outputReport += '<td align=\'right\'>' + "{:,.{}f}".format(totalNotionalUSD, 0) + '</td>'
            outputReport += '<td align =\'right\'>' + "{:,.{}f}".format(totalNotionalNative, 0) + '</td>'
            outputReport += '</tr>'

            with open(self.template) as file:
                templateContent = file.read()
                content = templateContent.replace('###Template###', outputReport)
                outputFile = open(self.HTMLReportFullOutPath, 'w')
                outputFile.write(content)
                logger.info(content)
                outputFile.close()
        except Exception as e:
            logger.info('%s', str(e))
        return content

    def DetailReport(self, dbConn):
        cursorDetail = dbConn.cursor()

        with open(self.SQLDetail) as file:
            sqlDetail = file.read()
        sqlDetail = sqlDetail.replace('{USDCNH_FX}', self.defaultFX)
        logger.info("SQLDetail query = %s", sqlDetail)
        cursorDetail.execute(sqlDetail)
        resultDetail = cursorDetail.fetchall()
        logger.info('Records = [ %i ]', len(resultDetail))

        workbook = xlsxwriter.Workbook(self.ExcelReportFullOutPath)
        self.ExcelGenerator(resultDetail, workbook)
        content = self.HtmlGenerator(resultDetail)

        workbook.close()
        cursorDetail.close()
        #GTJAEmailSender.sendEmail(content, businessDate, dateFormat, True)
        GTJAAttachmentEmailSender.sendEmail(content, self.ExcelReportFullOutPath, 'xlsx', businessDate, dateFormat)

    def main(self):
        logger.info('NotionalDetail Parent Class: main()')
        sophisDb = SophisDBConnection()
        dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                                   sophisDb.getHost() + "/" + sophisDb.getDbInstance())

        logger.info(dbConn)
        self.DetailReport(dbConn)
        dbConn.close()

    if __name__ == "__main__":
        main()
