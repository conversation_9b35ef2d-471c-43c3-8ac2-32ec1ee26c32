## Date         Name                       Detail
##==========================================================================================================================
##20200605      <PERSON>                 This is a script to check the Early Exercise Report
##20200904      Chris <PERSON>u                  Generate the HTML report to business if there is any dividend pay out on next business date.
##20210309      Chris <PERSON>                  Handle the unavailable reports and listed option to avoid throw exception
##
##
from Sophis.SophisDelta import SophisDelta
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
from datetime import datetime, timedelta
import logging
import cx_Oracle
import datetime
import smtplib
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage

dateFormat = "YYYYMMDD"

def sendEmail(content, today_str):
    logger.info("Try to send email")
    sender = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Sender')
    receiptent = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Receiptent')
    subject = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('Subject')
    smptHost = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_HOST')
    smptPort = PropertiesUtil.configObject.GetConfigProperties()["Email"].get('SMTP_PORT')

    subject = subject.replace(dateFormat, today_str)

    subject = subject
    msg = EmailMessage()
    msg['From'] = sender
    msg['To'] = receiptent
    msg['Subject'] = subject
    msg.set_content(content)
    msg.add_alternative(content, subtype='html')
    logger.info("================================================================")
    logger.info(msg)
    if (PropertiesUtil.configObject.IsPRD()):
        with smtplib.SMTP(smptHost, smptPort) as server:
            server.send_message(msg)
    else:
        logger.info("No email sending for DEV or UAT")


def getDelta(files, businessDate):
    fileList = files.split(';')
    delta = dict()
    for file in fileList:
        try:
            filename = file.replace('YYYYMMDD', businessDate)
            deltaValues = SophisDelta(filename)
            delta.update(deltaValues.getDelta())
        except BaseException as ex:
            logger.error(ex)
    return delta


def deltaLookup(option):
    try:
        zero = '0000'
        try:
            idx = option.index('.')
            if (idx > 0):
                suffix = option[idx + 1:]
                num = 4 - len(suffix)
                option = option + zero[0:num]
        except ValueError:
            logger.error('No Decimal Point [' + option + ']')
        delta = deltaMap[option]

    except KeyError:
        logger.error('Key Error ' + option)
        return ''
    return delta


def getLastPriceDate(day):
    weekOfDay = day.strftime('%a')
    logger.info('Check report date: %s', weekOfDay)
    if (weekOfDay == 'Mon'):
        return (datetime.date.today() - timedelta(3)).strftime('%Y%m%d')
    return (datetime.date.today() - timedelta(1)).strftime('%Y%m%d')

def getNextBusinessDate(day):
    weekOfDay = day.strftime('%a')
    logger.info('next business date : %s', weekOfDay)
    if (weekOfDay == 'Fri'):
        return (datetime.date.today() + timedelta(3))
    return (datetime.date.today() + timedelta(1))

today_str = datetime.date.today().strftime('%Y%m%d')
logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Header')
reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFolder')
reportFileName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFileName')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
reportFileName = reportFileName.replace('YYYYMMDD', today_str)
reportOutput = open(reportFolder + "/" + reportFileName, 'w')

intraday = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('intraday')

if ( intraday == None ):
    # day = datetime.date.today() - timedelta(1)
	businessDate = getLastPriceDate(datetime.date.today())
	lastPriceDate = businessDate
else:
	businessDate = datetime.date.today().strftime('%Y%m%d')
	lastPriceDate = (datetime.date.today()-timedelta(1)).strftime('%Y%m%d')

nextBusinessDate = getNextBusinessDate(datetime.date.today())
#businessDate = datetime.date.today().strftime('%Y%m%d')
#lastPriceDate = day

deltaFile = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('deltaFiles')
if (intraday == None):
    deltaMap = getDelta(deltaFile, lastPriceDate)
else:
    deltaMap = getDelta(deltaFile, businessDate)

logger.info('Template: %s', template);
logger.info('Report Date: %s', businessDate)
logger.info('Last Price Date: %s', lastPriceDate)
logger.info('SQL File = %s', SQL)

logger.info('Report Date = %s', today_str)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()
with open(SQL) as file: sql = file.read()
cursor.execute(sql, reportDate=businessDate, lastPriceDate=lastPriceDate)
logger.info('SQL = %s', sql)
results = cursor.fetchall()
logger.info("Records = [ %i ]", len(results))
reportOutput.write(header)
reportOutput.write('\n')
htmlOutput = ""

for internalref in results:
    opcvm = internalref[0]
    sicovam = internalref[1]
    quantity = internalref[2]
    portfolio = internalref[3]
    optionName = internalref[4]
    optionStrike = internalref[5]
    optionExpirationDate = internalref[6]
    optionTheo = internalref[7]
    businessDate = internalref[8]
    underlyingLastPrice = internalref[9]
    dividend = internalref[10]
    next_dividend_date = internalref[11]

    if (internalref[12] == 2):
        callPut = 'C'
    else:
        callPut = 'P'
    hibor = internalref[13]
    dayToExpiry = internalref[14]
    financialCost = internalref[15]
    underlyerReference = internalref[16]
    # RHS = internalref[16]
    # LHS = internalref[17]

    logger.info(internalref)

    delta = deltaLookup(optionName)
    logger.info(optionName + ' [' + delta + ']')
    reportOutput.write(underlyerReference)
    reportOutput.write(',')
    reportOutput.write(portfolio)
    reportOutput.write(',')
    reportOutput.write("{:.6f}".format(underlyingLastPrice))
    reportOutput.write(',')
    reportOutput.write(optionName)
    reportOutput.write(',')
    reportOutput.write(optionExpirationDate.strftime('%d-%b-%Y'))
    reportOutput.write(',')
    reportOutput.write("{:.6f}".format(optionStrike))
    reportOutput.write(',')
    reportOutput.write(callPut)
    reportOutput.write(',')
    reportOutput.write(str(quantity))
    reportOutput.write(',')
    reportOutput.write("{:.6f}".format(optionTheo))
    reportOutput.write(',')
    reportOutput.write("{:.6f}".format(dividend))
    reportOutput.write(',')
    reportOutput.write(next_dividend_date.strftime('%d-%b-%Y'))
    reportOutput.write(',')
    reportOutput.write(delta)
    reportOutput.write('\n')

    if ( next_dividend_date.date() == nextBusinessDate ):
        htmlOutput += "<tr>"
        htmlOutput += "<td>" + underlyerReference + "</td>"
        htmlOutput += "<td>" + portfolio + "</td>"
        htmlOutput += "<td>" + "{:.6f}".format(underlyingLastPrice) + "</td>"
        htmlOutput += "<td>" + optionName + "</td>"
        htmlOutput += "<td>" + optionExpirationDate.strftime('%d-%b-%Y') + "</td>"
        htmlOutput += "<td>" + "{:.6f}".format(optionStrike) + "</td>"
        htmlOutput += "<td>" + callPut + "</td>"
        htmlOutput += "<td>" + str(quantity) + "</td>"
        htmlOutput += "<td>" + "{:.6f}".format(optionTheo) + "</td>"
        htmlOutput += "<td>" + "{:.6f}".format(dividend) + "</td>"
        htmlOutput += "<td>" + next_dividend_date.strftime('%d-%b-%Y') + "</td>"
        htmlOutput += "<td>" + delta + "</td>"
        htmlOutput += "</tr>"

reportOutput.close()

if ( len(htmlOutput) > 0 ):
    logger.info("Check Html Output")
    with open(template) as file: templateContent = file.read()
    content = templateContent.replace('###Template###', htmlOutput)
    outputfile = open('output/EarlyExerciseReport.html', 'w')
    outputfile.write(content)
    outputfile.close()
    sendEmail(content, today_str)

logger.info("Program Exit")    
