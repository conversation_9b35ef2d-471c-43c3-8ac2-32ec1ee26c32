## Date         Name                       Detail
##==========================================================================================================================
#
from GTJA.SophisDBConnection import SophisDBConnection
from GTJA.GTJALogging import GTJALogging
from GTJA import PropertiesUtil
import csv
import cx_Oracle
import datetime

dateFormat = "YYYYMMDD"
decimal_point = 2

logFileName = PropertiesUtil.configObject.GetLogFileName()
log = GTJALogging(logFileName)
logger = log.GetLogger()



header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Header')
today_str = datetime.date.today().strftime('%Y%m%d')

#Report file
reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFolder')
SQL = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('SQL')
header = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('Header')
reportFolder = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFolder')
reportFileName = PropertiesUtil.configObject.GetConfigProperties()['Output'].get('ReportFileName')
template = PropertiesUtil.configObject.GetConfigProperties()['Input'].get('template')
reportFileName = reportFileName.replace('YYYYMMDD', today_str)
#reportOutput = open(reportFolder + "/" + reportFileName, 'w')

logger.info('SQL = %s', SQL)
logger.info('Report Date = %s', today_str)
sophisDb = SophisDBConnection()
dbConn = cx_Oracle.connect(sophisDb.getUserId(), sophisDb.getPassword(),
                           sophisDb.getHost() + "/" + sophisDb.getDbInstance())
logger.info(dbConn)
cursor = dbConn.cursor()
with open(SQL) as file: sql = file.read()
logger.info("SQL query = %s", sql)
cursor.execute(sql)
results = cursor.fetchall()
logger.info('Records = [ %i ]', len(results))

outputReport = ''

headerList = header.split(",")
logger.info(list)

if (len(results) > 0):
    with open(reportFolder + "/" + reportFileName, mode='w', newline='') as csvfile:
        csvWriter = csv.writer(csvfile, delimiter=',')
        csvWriter.writerow(header.split(','))
        for internalref in results:
            masterSicovam = internalref[0]
            masterReference = internalref[1]
            masterLibelle = internalref[2]
            masterAllotment = internalref[3]
            slaveQuantity = internalref[4]
            slaveSicovam = internalref[5]
            slaveLibelle = internalref[6]
            slaveReference = internalref[7]
            currency = internalref[8]
            dayCountBasis = internalref[9]
            spread = internalref[10]
            interestRate = internalref[11]
            frequency = internalref[12]
            fixedRate = internalref[13]

            if slaveQuantity != 0:
                csvWriter.writerow([masterSicovam, masterReference, masterLibelle, masterAllotment, slaveQuantity, slaveSicovam, slaveLibelle, slaveReference, currency, dayCountBasis, spread, interestRate, frequency, fixedRate])
                logger.info('[%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s]', masterSicovam, masterReference, masterLibelle, masterAllotment, slaveQuantity, slaveSicovam, slaveLibelle, slaveReference, currency, dayCountBasis, spread, interestRate, frequency, fixedRate)
            else:
                logger.info('Skip [%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s]', masterSicovam, masterReference,
                            masterLibelle, masterAllotment, slaveQuantity, slaveSicovam, slaveLibelle, slaveReference,
                            currency, dayCountBasis, spread, interestRate, frequency, fixedRate)
        csvfile.close()