'''
Date        Name            Detail
========================================================================================================================
20250127    Alvin Mak       Add Credit Default Swaps (CDS) parser.
'''
import logging

from .default_product import default_product
from datetime import datetime


class cds(default_product):

    def __init__(self):
        self.__allotment__ = 'CDS'
        self.__header__ = list()
        self.__header__.append('Party ID')
        self.__header__.append('Counterparty')
        self.__header__.append('Trade ID')
        self.__header__.append('Product Class')
        self.__header__.append('Notional1')
        self.__header__.append('Trade Currency 1')
        self.__header__.append('Trade Date')
        self.__header__.append('Start Date')
        self.__header__.append('End Date')
        self.__header__.append('MtM')
        self.__header__.append('MtM currency')
        self.__header__.append('MtM( in USD)')
        self.__header__.append('Underlying Initial Spread')
        self.__header__.append('IR Index')

    def generate_positions(self, positions):
        csvRecords = list()
        for position in positions:
            if position['instrumentDetail']['allotment'] == self.__allotment__:
                expiry_date = datetime.strptime(position['endDate']['date'], '%Y-%m-%d')
                today = datetime.now()
                if today < expiry_date:
                    csvRecord = list()
                    try:
                        csvRecord.append(position['tradingEntity']['name'])
                        csvRecord.append(position['counterparty']['name'])
                        csvRecord.append(position['instrumentDetail']['referenceData']['reference'])
                        csvRecord.append(position['instrumentDetail']['allotment'])
                        csvRecord.append(position['notional1']['amount'])
                        csvRecord.append(position['notional1']['currency']['ISO'])
                        csvRecord.append(position['tradeDate']['date'])
                        csvRecord.append(position['tradeDate']['date'])  # Start Date = Trade Date
                        csvRecord.append(position['endDate']['date'])
                        csvRecord.append(position['markToMarket']['mtm']['amount'])
                        csvRecord.append(position['markToMarket']['mtm']['currency']['ISO'])
                        csvRecord.append(position['markToMarket']['mtmUSD']['amount'])
                        csvRecord.append(position['payingLegRate']['value'])
                        csvRecord.append(position['payingLegRate']['irIndex']['reference'])
                        csvRecords.append(csvRecord)
                    except Exception as err:
                        csvRecord.append('<<<' + str(err) + '>>>')
                else:
                    logging.warning(position['instrumentDetail']['referenceData']['reference'] + ' has expired!')
        return csvRecords
