from .default_product import default_product
from datetime import datetime
class equity_swap(default_product):

    def __init__(self):
        self.__allotment__= ['Equity Swap', 'Index Swap']
        self.__header__ = list()
        self.__header__.append('Party ID')
        self.__header__.append('Counterparty')
        self.__header__.append('Trade ID')
        self.__header__.append('Product Class')
        self.__header__.append('Underlying')
        self.__header__.append('Quantity')
        self.__header__.append('Notional1')
        self.__header__.append('Trade Currency 1')
        self.__header__.append('Trade Date')
        self.__header__.append('Start Date')
        self.__header__.append('End Date')
        self.__header__.append('MtM')
        self.__header__.append('MtM currency')
        self.__header__.append('MtM( in USD)')
        self.__header__.append('Underlying initial spread')
        self.__header__.append('IR Index')

    def generate_positions(self, positions):
        csvRecords = list()
        for position in positions:
            if position['instrumentDetail']['allotment'] in self.__allotment__:
                csvRecord = list()
                try:
                    if ( datetime.strptime(position['endDate']['date'], '%Y-%m-%d') > datetime.now() and position['quantity'] != 0):
                        csvRecord.append(position['tradingEntity']['name'])
                        csvRecord.append(position['counterparty']['name'])
                        csvRecord.append(position['instrumentDetail']['referenceData']['reference'])
                        csvRecord.append(position['instrumentDetail']['allotment'])
                        try:
                            csvRecord.append(position['underlying']['references']['TICKER'])
                        except:
                            csvRecord.append(position['underlying']['referenceData']['reference'])
                        csvRecord.append(position['quantity'])
                        csvRecord.append(position['notional1']['amount'])
                        csvRecord.append(position['notional1']['currency']['ISO'])
                        csvRecord.append(position['tradeDate']['date'])
                        csvRecord.append(position['startDate']['date'])
                        csvRecord.append(position['endDate']['date'])
                        csvRecord.append(position['markToMarket']['mtm']['amount'])
                        csvRecord.append(position['markToMarket']['mtm']['currency']['ISO'])
                        csvRecord.append(position['markToMarket']['mtmUSD']['amount'])
                        csvRecord.append(position['underlyingInitialSpread'])
                        csvRecord.append(position['irIndex']['reference'])
                        csvRecords.append(csvRecord)
                except Exception as err:
                    csvRecord.append('<<<'+str(err)+'>>>')

        return csvRecords