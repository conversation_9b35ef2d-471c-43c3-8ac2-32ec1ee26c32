'''
Date        Name            Detail
========================================================================================================================
20231127    <PERSON>      Add a portfolio column in the fxforward report.
'''
from .default_product import default_product
from datetime import datetime
class fx_forward(default_product):

    def __init__(self):
        self.__allotment__ = ['FX Forward', 'NDF']
        self.__header__ = list()
        self.__header__.append('Party ID')
        self.__header__.append('Counterparty')
        self.__header__.append('Ticket ID')
        self.__header__.append('Trade ID')
        self.__header__.append('Product Class')
        self.__header__.append('Underlying')
        self.__header__.append('Price')
        self.__header__.append('Notional1')
        self.__header__.append('Trade Currency 1')
        self.__header__.append('Notional2')
        self.__header__.append('Trade Currency 2')
        self.__header__.append('Trade Date')
        self.__header__.append('End Date')
        self.__header__.append('Current FX Forward Rate')
        self.__header__.append('MtM')
        self.__header__.append('MtM currency')
        self.__header__.append('MtM( in USD)')
        self.__header__.append('Portfolio')

    def generate_positions(self, positions):
        csv_records = list()
        for position in positions:
            if position['instrumentDetail']['allotment'] in self.__allotment__:
                for trade in position['Trades']:
                    csv_record = list()
                    try:
                        if ( datetime.strptime(trade['paymentDate']['date'], '%Y-%m-%d') > datetime.now() and trade['backofficeStatus'] != 'MO Cancelled'):
                            csv_record.append(trade['entity']['name'])
                            csv_record.append(trade['counterparty']['name'])
                            csv_record.append(trade['ticketId'])
                            csv_record.append(position['tradeId'])
                            csv_record.append(position['instrumentDetail']['allotment'])
                            csv_record.append(position['FxPair'])
                            csv_record.append(trade['grossPrice'])
                            csv_record.append(trade['notional1']['amount'])
                            csv_record.append(trade['notional1']['currency']['ISO'])
                            csv_record.append(trade['notional2']['amount'])
                            csv_record.append(trade['notional2']['currency']['ISO'])
                            csv_record.append(trade['tradeDate']['date'])
                            csv_record.append(trade['paymentDate']['date'])
                            csv_record.append(position['Theo'])
                            csv_record.append(trade['markToMarket']['mtm']['amount'])
                            csv_record.append(trade['markToMarket']['mtm']['currency']['ISO'])
                            csv_record.append(trade['markToMarket']['mtmUSD']['amount'])
                            csv_record.append(position['portfolio'])
                            csv_records.append(csv_record)
                    except Exception as err:
                        print(err, trade)
        return csv_records