'''
Date        Name            Detail
========================================================================================================================
20250128    Alvin Mak       Add Accumulators and Decumulators parser. This parser supports trade level extraction.
'''
import logging

from .default_product import default_product
from datetime import datetime


class accu_decu(default_product):

    def __init__(self):
        self.__allotment__ = ['Accumulators', 'Decumulators']
        self.__header__ = list()
        self.__header__.append('Party ID')
        self.__header__.append('Counterparty')
        self.__header__.append('Ticket ID')
        self.__header__.append('Trade ID')
        self.__header__.append('Product Class')
        self.__header__.append('Underlying')
        self.__header__.append('Notional1')
        self.__header__.append('Trade Currency 1')
        self.__header__.append('Trade Date')
        self.__header__.append('Start Date')
        self.__header__.append('End Date')
        self.__header__.append('MtM')
        self.__header__.append('MtM currency')
        self.__header__.append('MtM( in USD)')

    def generate_positions(self, positions):
        csv_records = list()
        for position in positions:
            if position['instrumentDetail']['allotment'] in self.__allotment__:
                for trade in position['TradeDetails']:
                    expiry_date = datetime.strptime(position['endDate']['date'], '%Y-%m-%d')
                    quantity = (trade['quantity'])
                    today = datetime.now()
                    if today < expiry_date and quantity != 0:
                        csv_record = list()
                        try:
                            csv_record.append(trade['entity']['name'])
                            csv_record.append(trade['counterparty']['name'])
                            csv_record.append(trade['ticketId'])
                            csv_record.append(position['tradeId'])
                            csv_record.append(position['instrumentDetail']['allotment'])
                            csv_record.append(position['ClauseUnderlying']['referenceData']['reference'])
                            csv_record.append(trade['notional1']['amount'])
                            csv_record.append(trade['notional1']['currency']['ISO'])
                            csv_record.append(trade['tradeDate']['date'])
                            csv_record.append(position['IssueDate']['date'])
                            csv_record.append(position['endDate']['date'])
                            csv_record.append(trade['markToMarket']['mtm']['amount'])
                            csv_record.append(trade['markToMarket']['mtm']['currency']['ISO'])
                            csv_record.append(trade['markToMarket']['mtmUSD']['amount'])
                            csv_records.append(csv_record)
                        except Exception as err:
                            csv_record.append('<<<' + str(err) + '>>>')
                    else:
                        logging.warning(position['instrumentDetail']['referenceData']['reference'] + ' has expired!')
        return csv_records
