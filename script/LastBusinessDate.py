import datetime

config=open('./LastBusinessDate.txt', 'w+')

today = datetime.date.today()
print("Today's date:", today)
Day = today.strftime('%A')
YYYYMMDD = today.strftime('%Y%m%d')

print("Day is:", Day)
if Day == 'Monday':
    print("Delta is ", 3)
    no_of_days = datetime.timedelta(days=3)
else:
    print("Delta is ", 1)
    no_of_days = datetime.timedelta(days=1)
last = today - no_of_days
SophisLastDate = last.strftime('%m/%d/%y')
lastYYYYMMDD = last.strftime('%Y%m%d')
print('LAST=', last, file=config,sep='')
print('SOPHIS_LAST_BUSINESS_DATE=',SophisLastDate, file=config,sep='')
print('YYYYMMDDT-1=',lastYYYYMMDD, file=config,sep='')
