#!/usr/bin/env python3
"""
<PERSON>ript to identify and fix common quantity field issues in HKTR generators
"""

def safe_get_quantity(data_dict, field_path, default=0):
    """
    Safely get quantity value from nested dictionary
    
    Args:
        data_dict: Dictionary to search in
        field_path: String path like 'trade.quantity' or just 'quantity'
        default: Default value if field not found
    
    Returns:
        Numeric quantity value
    """
    try:
        if '.' in field_path:
            # Handle nested path like 'trade.quantity'
            keys = field_path.split('.')
            value = data_dict
            for key in keys:
                value = value[key]
        else:
            # Handle simple field like 'quantity'
            value = data_dict[field_path]
        
        # Convert to float to handle string numbers
        if isinstance(value, str):
            return float(value)
        elif isinstance(value, (int, float)):
            return float(value)
        else:
            print(f"Warning: Unexpected quantity type {type(value)}: {value}")
            return default
            
    except (KeyError, ValueError, TypeError) as e:
        print(f"Warning: Could not get quantity from {field_path}: {e}")
        return default

def is_non_zero_quantity(quantity_value):
    """
    Check if quantity is non-zero, handling various data types
    
    Args:
        quantity_value: The quantity value to check
        
    Returns:
        Boolean indicating if quantity is non-zero
    """
    try:
        # Convert to float for comparison
        if isinstance(quantity_value, str):
            qty_float = float(quantity_value)
        elif isinstance(quantity_value, (int, float)):
            qty_float = float(quantity_value)
        else:
            return False
        
        # Check if non-zero (with small tolerance for floating point)
        return abs(qty_float) > 1e-10
        
    except (ValueError, TypeError):
        return False

def debug_quantity_access(position, trade):
    """
    Debug function to print all quantity-related information
    """
    print("DEBUG: Quantity Analysis")
    print("-" * 30)
    
    # Check position quantity
    pos_qty = safe_get_quantity(position, 'quantity')
    print(f"position['quantity']: {pos_qty}")
    
    # Check trade quantity
    trade_qty = safe_get_quantity(trade, 'quantity')
    print(f"trade['quantity']: {trade_qty}")
    
    # Check comparisons
    print(f"position quantity != 0: {is_non_zero_quantity(pos_qty)}")
    print(f"trade quantity != 0: {is_non_zero_quantity(trade_qty)}")
    
    # Check raw values
    if 'quantity' in position:
        print(f"Raw position quantity: {position['quantity']} (type: {type(position['quantity'])})")
    if 'quantity' in trade:
        print(f"Raw trade quantity: {trade['quantity']} (type: {type(trade['quantity'])})")
    
    print()

# Example of how to fix the quantity checking in generators
def fixed_quantity_check_example():
    """
    Example of how to properly check quantities in the generator code
    """
    
    # BEFORE (problematic):
    # if trade['quantity'] != 0:
    
    # AFTER (fixed):
    # trade_qty = safe_get_quantity(trade, 'quantity')
    # position_qty = safe_get_quantity(position, 'quantity') 
    # if is_non_zero_quantity(trade_qty) and is_non_zero_quantity(position_qty):
    
    pass

# Suggested improvements for the generator classes
GENERATOR_FIXES = """
SUGGESTED FIXES FOR HKTR GENERATORS:

1. Replace direct quantity access:
   OLD: if trade['quantity'] != 0:
   NEW: if is_non_zero_quantity(safe_get_quantity(trade, 'quantity')):

2. Replace direct quantity output:
   OLD: csv_record.append(trade['quantity'])
   NEW: csv_record.append(safe_get_quantity(trade, 'quantity'))

3. Add debugging for problematic records:
   if not is_non_zero_quantity(safe_get_quantity(trade, 'quantity')):
       debug_quantity_access(position, trade)

4. Consistent quantity field usage:
   - Decide whether to use position['quantity'] or trade['quantity']
   - Document which field represents what
   - Use the same field consistently across all generators

5. Add validation:
   - Check if required quantity fields exist
   - Log warnings for missing or invalid quantities
   - Handle edge cases gracefully
"""

if __name__ == "__main__":
    print("Quantity Issue Fix Utilities")
    print("=" * 40)
    print(GENERATOR_FIXES)
    
    # Test the utility functions
    print("\nTesting utility functions:")
    print("-" * 30)
    
    test_cases = [
        {"quantity": 1.0},
        {"quantity": "1.0"},
        {"quantity": 0},
        {"quantity": "0"},
        {"quantity": ""},
        {},  # missing quantity
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"Test case {i+1}: {test_case}")
        qty = safe_get_quantity(test_case, 'quantity')
        is_nonzero = is_non_zero_quantity(qty)
        print(f"  safe_get_quantity: {qty}")
        print(f"  is_non_zero_quantity: {is_nonzero}")
        print()
