#!/usr/bin/env python3
"""
Debug script to investigate quantity field issues in JSON data
"""
import json
import sys
from pprint import pprint

def analyze_json_structure(json_file_path):
    """Analyze the JSON structure to understand quantity fields"""
    
    print("=" * 80)
    print("JSON QUANTITY FIELD ANALYSIS")
    print("=" * 80)
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✓ Successfully loaded JSON file: {json_file_path}")
        print(f"✓ Total positions: {len(data)}")
        print()
        
        # Analyze first few positions
        for i, position in enumerate(data[:3]):  # Check first 3 positions
            print(f"POSITION {i+1}:")
            print("-" * 40)
            
            # Check position-level quantity
            if 'quantity' in position:
                pos_qty = position['quantity']
                print(f"  position['quantity']: {pos_qty} (type: {type(pos_qty)})")
            else:
                print("  position['quantity']: NOT FOUND")
            
            # Check trades
            if 'Trades' in position:
                print(f"  Number of trades: {len(position['Trades'])}")
                
                for j, trade in enumerate(position['Trades'][:2]):  # Check first 2 trades
                    print(f"    TRADE {j+1}:")
                    
                    if 'quantity' in trade:
                        trade_qty = trade['quantity']
                        print(f"      trade['quantity']: {trade_qty} (type: {type(trade_qty)})")
                    else:
                        print("      trade['quantity']: NOT FOUND")
                    
                    # Check for other quantity-related fields
                    qty_fields = [k for k in trade.keys() if 'quantity' in k.lower() or 'qty' in k.lower()]
                    if qty_fields:
                        print(f"      Other quantity fields: {qty_fields}")
                        for field in qty_fields:
                            print(f"        {field}: {trade[field]} (type: {type(trade[field])})")
            else:
                print("  Trades: NOT FOUND")
            
            # Check instrument details
            if 'instrumentDetail' in position and 'allotment' in position['instrumentDetail']:
                allotment = position['instrumentDetail']['allotment']
                print(f"  Allotment: {allotment}")
            
            print()
        
        # Summary analysis
        print("SUMMARY ANALYSIS:")
        print("-" * 40)
        
        position_qty_count = 0
        trade_qty_count = 0
        zero_position_qty = 0
        zero_trade_qty = 0
        
        for position in data:
            # Count position quantities
            if 'quantity' in position:
                position_qty_count += 1
                if position['quantity'] == 0 or position['quantity'] == "0":
                    zero_position_qty += 1
            
            # Count trade quantities
            if 'Trades' in position:
                for trade in position['Trades']:
                    if 'quantity' in trade:
                        trade_qty_count += 1
                        if trade['quantity'] == 0 or trade['quantity'] == "0":
                            zero_trade_qty += 1
        
        print(f"Positions with 'quantity' field: {position_qty_count}/{len(data)}")
        print(f"Positions with zero quantity: {zero_position_qty}")
        print(f"Trades with 'quantity' field: {trade_qty_count}")
        print(f"Trades with zero quantity: {zero_trade_qty}")
        
        # Check for specific problematic cases
        print("\nPROBLEMATIC CASES:")
        print("-" * 40)
        
        for i, position in enumerate(data):
            if 'quantity' in position and 'Trades' in position:
                pos_qty = position['quantity']
                
                for j, trade in enumerate(position['Trades']):
                    if 'quantity' in trade:
                        trade_qty = trade['quantity']
                        
                        # Check if quantities don't match or are unexpected
                        if str(pos_qty) != str(trade_qty):
                            print(f"Position {i}: position.quantity={pos_qty}, trade.quantity={trade_qty}")
                        
                        # Check if quantity is 1.0 in JSON but becomes 0 in processing
                        if (pos_qty == 1.0 or pos_qty == "1.0") and (trade_qty == 0 or trade_qty == "0"):
                            print(f"Position {i}: FOUND ISSUE - pos_qty={pos_qty}, trade_qty={trade_qty}")
                        
                        if (trade_qty == 1.0 or trade_qty == "1.0") and (pos_qty == 0 or pos_qty == "0"):
                            print(f"Position {i}: FOUND ISSUE - trade_qty={trade_qty}, pos_qty={pos_qty}")
        
    except FileNotFoundError:
        print(f"❌ Error: File not found: {json_file_path}")
    except json.JSONDecodeError as e:
        print(f"❌ Error: Invalid JSON: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_quantity_comparison():
    """Test different quantity value comparisons"""
    print("\nQUANTITY COMPARISON TESTS:")
    print("-" * 40)
    
    test_values = [0, 0.0, "0", "0.0", 1, 1.0, "1", "1.0"]
    
    for val in test_values:
        print(f"Value: {val} (type: {type(val).__name__})")
        print(f"  val != 0: {val != 0}")
        print(f"  val == 0: {val == 0}")
        print(f"  bool(val): {bool(val)}")
        print()

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python debug_quantity_issue.py <json_file_path>")
        print("Example: python debug_quantity_issue.py input/20250128_HKTR.json")
        sys.exit(1)
    
    json_file = sys.argv[1]
    analyze_json_structure(json_file)
    test_quantity_comparison()
